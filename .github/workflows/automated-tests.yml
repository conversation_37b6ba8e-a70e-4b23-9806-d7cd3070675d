name: 'Automated tests'
on:
  push:
    branches:
      - 'develop'
      - 'v2.[5-9].x-release'
      - 'v[3-9].*.x-release'
  pull_request:
    types: [opened, synchronize, reopened]
permissions:
  contents: read
jobs:
  build-install-and-test:
    runs-on: ubuntu-20.04
    steps:
      - uses: actions/checkout@v2
      - run: ./build/get_external_dependencies.sh
      - run: ./build/setup.sh bbb-apps-akka
      - run: ./build/setup.sh bbb-config
      - run: ./build/setup.sh bbb-etherpad
      - run: ./build/setup.sh bbb-freeswitch-core
      - run: ./build/setup.sh bbb-freeswitch-sounds
      - run: ./build/setup.sh bbb-fsesl-akka
      - run: ./build/setup.sh bbb-html5
      - run: ./build/setup.sh bbb-learning-dashboard
      - run: ./build/setup.sh bbb-libreoffice-docker
      - run: ./build/setup.sh bbb-mkclean
      - run: ./build/setup.sh bbb-pads
      - run: ./build/setup.sh bbb-playback
      - run: ./build/setup.sh bbb-playback-notes
      - run: ./build/setup.sh bbb-playback-podcast
      - run: ./build/setup.sh bbb-playback-presentation
      - run: ./build/setup.sh bbb-playback-screenshare
      - run: ./build/setup.sh bbb-record-core
      - run: ./build/setup.sh bbb-web
      - run: ./build/setup.sh bbb-webrtc-sfu
      - run: ./build/setup.sh bigbluebutton
      - run: tar cvf artifacts.tar artifacts/
      - name: Archive packages
        uses: actions/upload-artifact@v3
        with:
          name: artifacts.tar
          path: |
            artifacts.tar
      # - name: Fake package build
      #   run: |
      #     sudo sh -c '
      #     echo "Faking a package build (to speed up installation test)"
      #     cd /
      #     wget -q "http://ci.bbbvm.imdt.com.br/artifacts.tar"
      #     tar xf artifacts.tar
      #     '
      - name: Generate CA
        run: |
          sudo sh -c '
          mkdir /root/bbb-ci-ssl/
          cd /root/bbb-ci-ssl/
          openssl rand -base64 48 > /root/bbb-ci-ssl/bbb-dev-ca.pass ;
          chmod 600 /root/bbb-ci-ssl/bbb-dev-ca.pass ;
          openssl genrsa -des3 -out bbb-dev-ca.key -passout file:/root/bbb-ci-ssl/bbb-dev-ca.pass 2048 ;
          openssl req -x509 -new -nodes -key bbb-dev-ca.key -sha256 -days 1460 -passin file:/root/bbb-ci-ssl/bbb-dev-ca.pass -out bbb-dev-ca.crt -subj "/C=CA/ST=BBB/L=BBB/O=BBB/OU=BBB/CN=BBB-DEV" ;
          '
      - name: Trust CA
        run: |
          sudo sh -c '
          sudo mkdir /usr/local/share/ca-certificates/bbb-dev/
          sudo cp /root/bbb-ci-ssl/bbb-dev-ca.crt /usr/local/share/ca-certificates/bbb-dev/
          sudo chmod 644 /usr/local/share/ca-certificates/bbb-dev/bbb-dev-ca.crt
          sudo update-ca-certificates
          '
      - name: Generate certificate
        run: |
          sudo sh -c '
          cd /root/bbb-ci-ssl/
          echo "127.0.0.1 bbb-ci.test" >> /etc/hosts
          openssl genrsa -out bbb-ci.test.key 2048
          rm bbb-ci.test.csr bbb-ci.test.crt bbb-ci.test.key
          cat > bbb-ci.test.ext << EOF
          authorityKeyIdentifier=keyid,issuer
          basicConstraints=CA:FALSE
          keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
          subjectAltName = @alt_names
          [alt_names]
          DNS.1 = bbb-ci.test
          EOF
          openssl req -nodes -newkey rsa:2048 -keyout bbb-ci.test.key -out bbb-ci.test.csr -subj "/C=CA/ST=BBB/L=BBB/O=BBB/OU=BBB/CN=bbb-ci.test" -addext "subjectAltName = DNS:bbb-ci.test"
          openssl x509 -req -in bbb-ci.test.csr -CA bbb-dev-ca.crt -CAkey bbb-dev-ca.key -CAcreateserial -out bbb-ci.test.crt -days 825 -sha256 -passin file:/root/bbb-ci-ssl/bbb-dev-ca.pass -extfile bbb-ci.test.ext
          cd

          mkdir -p /local/certs/
          cp /root/bbb-ci-ssl/bbb-dev-ca.crt /local/certs/
          cat /root/bbb-ci-ssl/bbb-ci.test.crt > /local/certs/fullchain.pem
          cat /root/bbb-ci-ssl/bbb-dev-ca.crt >> /local/certs/fullchain.pem
          cat /root/bbb-ci-ssl/bbb-ci.test.key > /local/certs/privkey.pem
          '
      - name: Setup local repository
        run: |
            sudo sh -c '
            apt install -yq dpkg-dev
            cd /root && wget -q http://ci.bbbvm.imdt.com.br/cache-3rd-part-packages.tar
            cp -r /home/<USER>/work/bigbluebutton/bigbluebutton/artifacts/ /artifacts/
            cd /artifacts && tar xf /root/cache-3rd-part-packages.tar
            cd /artifacts && dpkg-scanpackages . /dev/null | gzip -9c > Packages.gz
            echo "deb [trusted=yes] file:/artifacts/ ./" >> /etc/apt/sources.list
            '
      - name: Prepare for install
        run: |
            sudo sh -c '
            apt --purge -y remove apache2-bin
            '
      - name: Install BBB
        run: |
            sudo sh -c '
            cd /root/ && wget -q https://ubuntu.bigbluebutton.org/bbb-install-2.5.sh -O bbb-install.sh
            cat bbb-install.sh | sed "s|> /etc/apt/sources.list.d/bigbluebutton.list||g" | bash -s -- -v focal-25-dev -s bbb-ci.test -j -d /certs/
            bbb-conf --salt bbbci
            echo "NODE_EXTRA_CA_CERTS=/usr/local/share/ca-certificates/bbb-dev/bbb-dev-ca.crt" >> /usr/share/meteor/bundle/bbb-html5-with-roles.conf
            bbb-conf --restart
            '
      - name: Install test dependencies
        working-directory: ./bigbluebutton-tests/playwright
        run: |
          sh -c '
          npm install
          npx playwright install-deps
          npx playwright install
          '
      - name: Run tests
        working-directory: ./bigbluebutton-tests/playwright
        env:
          NODE_EXTRA_CA_CERTS: /usr/local/share/ca-certificates/bbb-dev/bbb-dev-ca.crt
          ACTIONS_RUNNER_DEBUG: true
          BBB_URL: https://bbb-ci.test/bigbluebutton/api
          BBB_SECRET: bbbci
        run: npm run test-chromium-ci
      - name: Run Firefox tests
        working-directory: ./bigbluebutton-tests/playwright
        if: ${{ contains(github.event.pull_request.labels.*.name, 'test Firefox')
                || contains(github.event.pull_request.labels.*.name, 'Test Firefox') }}
        env:
          NODE_EXTRA_CA_CERTS: /usr/local/share/ca-certificates/bbb-dev/bbb-dev-ca.crt
          ACTIONS_RUNNER_DEBUG: true
          BBB_URL: https://bbb-ci.test/bigbluebutton/api
          BBB_SECRET: bbbci
        # patch playwright's firefox so that it uses the system's root certificate authority
        run: |
          sh -c '
          find $HOME/.cache/ms-playwright -name libnssckbi.so -exec rm {} \; -exec ln -s /usr/lib/x86_64-linux-gnu/pkcs11/p11-kit-trust.so {} \;
          npm run test-firefox-ci
          '
      - if: always()
        uses: actions/upload-artifact@v3
        with:
          name: tests-report
          path: |
            bigbluebutton-tests/playwright/playwright-report
            bigbluebutton-tests/playwright/test-results
