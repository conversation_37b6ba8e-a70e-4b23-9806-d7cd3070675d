public:
  app:
    mobileFontSize: 16px
    desktopFontSize: 14px
    audioChatNotification: false
    # Shows the audio modal when user joins the room. The audio modal prompts
    # user to select an option ("Microphone" and/or "Listen only") for joining
    # audio
    autoJoin: true
    # Disables the listen only option in audio modal.
    listenOnlyMode: true
    forceListenOnly: false
    # Skips the echo test when connecting with microphone.
    skipCheck: true
    # Skips the echo test when connecting with microphone right after user
    # joins the room the first time. Subsequent joins to microphone won't
    # have echo test skipped, for example if user leaves and joins the mic
    # again or reloading page and joining mic again.
    # This setting won't have effect if skipCheck = true
    skipCheckOnJoin: true
    #
    # Allow users to change microphone/speaker dynamically
    # The device is changed immediately, without the need to rejoin
    # audio. Default value is true
    # Firefox users: if no output devices is shown, you may set the flag
    # "media.setsinkid.enabled" to make it work properly
    # enableDynamicAudioDeviceSelection: true
    #
    clientTitle: PBMEET | PolicyBazaar.com
    appName: PBMEET 2.0
    appVersion: 2.0.0
    bbbServerVersion: HTML5_FULL_BBB_VERSION
    displayBbbServerVersion: true
    copyright: ~
    html5ClientBuild: HTML5_CLIENT_VERSION
    helpLink: ~
    delayForUnmountOfSharedNote: 120000
    bbbTabletApp:
      enabled: false
      iosAppStoreUrl: 'https://apps.apple.com/us/app/bigbluebutton-tablet/id1641156756'
      iosAppUrlScheme: 'bigbluebutton-tablet'
    lockOnJoin: true
    cdn: ''
    basename: '/html5client'
    serverUrl: 'https://pbmeetqa.policybazaar.com'
    pbmeetV1BaseUrl_QA: 'https://videomeet.policybazaar.com' 
    pbmeetV1BaseUrl_LIVE: 'https://pbmeet.policybazaar.com' 
    # the base location of the BBB API. If you use a cluster setup with a load
    # balancer which hides the individual nodes, then this should be
    # https://bbb-host/bigbluebutton
    # If you run a traditional setup of multiple nodes behind scalelite and the
    # users see the hostnames of the individual nodes, you can leave this at the
    # default setting
    bbbWebBase: '/bigbluebutton'
    # If you run a cluster setup with a load balancer which hides the individual
    # nodes, then this should be set to https://bbb-host/learning-analytics-dashboard
    learningDashboardBase: '/learning-analytics-dashboard'
    # Use https URL of CSS file. Example: https://docs.bigbluebutton.org/administration/customize
    customStyleUrl: null
    darkTheme:
      enabled: true
    askForFeedbackOnLogout: true
    askForFeedbackToCustomerOnLogout: false
    # the default logoutUrl matches window.location.origin i.e. bigbluebutton.org for demo.bigbluebutton.org
    # in some cases we want only custom logoutUrl to be used when provided on meeting create. Default value: true
    askForConfirmationOnLeave: true
    allowDefaultLogoutUrl: true
    allowUserLookup: false
    dynamicGuestPolicy: true
    enableGuestLobbyMessage: true
    guestPolicyExtraAllowOptions: true
    alwaysShowWaitingRoomUI: true
    enableLimitOfViewersInWebcam: false
    enableMultipleCameras: true
    # Allow users to open webcam video modal/preview when video is already
    # active. This also allows to change virtual backgrounds without
    # restarting webcam.
    enableWebcamSelectorButton: true
    enableTalkingIndicator: true
    enableCameraBrightness: true
    mirrorOwnWebcam: false
    viewersInWebcam: 8
    ipv4FallbackDomain: ''
    allowLogout: true
    allowFullscreen: true
    preloadNextSlides: 2
    warnAboutUnsavedContentOnMeetingEnd: false
    # Allows users to enable automatic transcription when joining a meeting.
    # Automatic transcription requires the browser to support the web
    # speech API, which involves sending voice data to third-party servers!
    # https://developer.mozilla.org/en-US/docs/Web/API/Web_Speech_API/Using_the_Web_Speech_API#speech_recognition
    audioCaptions:
      enabled: false
      # mobile: <Boolean> - controls speech transcription availability on mobile
      mobile: false
      language:
        available:
        # - de-DE
          - en-US
          - es-ES
        # - fr-FR
        # - hi-ID
        # - it-IT
        # - ja-JP
          - pt-BR
        # - ru-RU
        # - zh-CN
        # If true, automatically uses the below locale field content as transcription language
        # and the language selector in audio modal won't show up!
        forceLocale: false
        # If true, the default selected value for language selector in audio modal
        # is the below locale field content
        defaultSelectLocale: true
        # Possible Values:
        # browserLanguage: to set browser language
        # [en-US, es-ES, pt-BR,...]: to set a specific locale
        # disabled: to set disabled
        locale: disabled
    mutedAlert:
      enabled: true
      interval: 200
      threshold: -50
      duration: 4000
    remainingTimeThreshold: 30
    remainingTimeAlertThresholdArray: [1,5]
    enableDebugWindow: true
    # Warning: increasing the limit of breakout rooms per meeting
    # can generate excessive overhead to the server. We recommend
    # this value to be kept under 16.
    breakouts:
      allowUserChooseRoomByDefault: false
      captureWhiteboardByDefault: false
      captureSharedNotesByDefault: false
      breakoutRoomLimit: 16
      sendInvitationToIncludedModerators: false
    # https://github.com/bigbluebutton/bigbluebutton/pull/10826
    customHeartbeat: false
    showAllAvailableLocales: true
    # Show "Audio Filters for Microphone" option in settings menu.
    # When set to true, users are able to enable/disable microphone constraints,
    # otherwise default values for 'microphoneConstraints' option
    # are used.
    # For more info, see 'microphoneConstraints' option in this config.
    # If not set, default value is true.
    showAudioFilters: true
    raiseHandActionButton:
      enabled: true
    # If enabled, before joining microphone the client will perform a trickle
    # ICE against Kurento and use the information about successfull
    # candidate-pairs to filter out local candidates in SIP.js's SDP.
    # Try enabling this setting in scenarios where the listenonly mode works,
    # but microphone doesn't (for example, when using VPN).
    # For compatibility check "Browser compatbility" section in:
    # https://developer.mozilla.org/en-US/docs/Web/API/RTCDtlsTransport/iceTransport
    # This is an EXPERIMENTAL setting and the default value is false
    # experimentalUseKmsTrickleIceForMicrophone: false
    #
    # Shows stats about download and upload rates, audio jitter, lost packets
    # and turn information
    enableNetworkStats: false
    # Enable the button to allow users to copy network stats to clipboard
    enableCopyNetworkStatsButton: true
    # where should client settings be stored? if you run a single BBB server or
    # a cluster with a reverse proxy in front of it, you may set this to 'local'
    # See See https://docs.bigbluebutton.org/administration/cluster-proxy
    # allowed values:
    # 'session' -> settings are stored in browser sessionStorage
    # 'local' -> settings are stored in browser localStorage
    userSettingsStorage: session
    defaultSettings:
      application:
        animations: true
        chatAudioAlerts: true
        chatPushAlerts: true
        userJoinAudioAlerts: true
        userJoinPushAlerts: true
        userLeaveAudioAlerts: true
        userLeavePushAlerts: true
        raiseHandAudioAlerts: true
        raiseHandPushAlerts: true
        guestWaitingAudioAlerts: true
        guestWaitingPushAlerts: true
        paginationEnabled: true
        whiteboardToolbarAutoHide: false
        darkTheme: false
        # fallbackLocale: if the locale the client is loaded in does not have a
        # translation a string, it will use the translation from the locale
        # specified in fallbackLocale. Note that fallbackLocale should be a
        # 100% translated locale for best user experience
        fallbackLocale: en
        # overrideLocale (default is null): if set (for example to 'de') will
        # force all clients to display the German translations of the strings.
        # Users can individually set their preferred locale through Settings,
        # but on first page load overrideLocale will trump the browser's
        # preferred locale
        overrideLocale: null
        #Audio constraints for microphone. Use this to control browser's
        #filters, such as AGC (Auto Gain Control) , Echo Cancellation,
        #Noise Supression, etc.
        #For more deails, see:
        # https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackConstraints
        #Currently, google chrome sets {ideal: true} for autoGainControl,
        #echoCancellation and noiseSuppression, if not set.
        #The accepted value for each constraint is an object of type
        #https://developer.mozilla.org/en-US/docs/Web/API/ConstrainBoolean
        #These values are used as initial constraints for every new participant,
        #and can be changed by user in: Settings > Application > Microphone
        #Audio Filters.
        # microphoneConstraints:
        #   autoGainControl:
        #     ideal: true
        #   echoCancellation:
        #     ideal: true
        #   noiseSuppression:
        #     ideal: true
      audio:
        inputDeviceId: undefined
        outputDeviceId: undefined
      dataSaving:
        viewParticipantsWebcams: true
        viewScreenshare: true
    shortcuts:
      openOptions:
        accesskey: O
        descId: openOptions
      toggleUserList:
        accesskey: U
        descId: toggleUserList
      toggleMute:
        accesskey: M
        descId: toggleMute
      joinAudio:
        accesskey: J
        descId: joinAudio
      leaveAudio:
        accesskey: L
        descId: leaveAudio
      togglePublicChat:
        accesskey: P
        descId: togglePublicChat
      hidePrivateChat:
        accesskey: H
        descId: hidePrivateChat
      closePrivateChat:
        accesskey: G
        descId: closePrivateChat
      raiseHand:
        accesskey: R
        descId: raiseHand
      openActions:
        accesskey: A
        descId: openActions
      openDebugWindow:
        accesskey: K
        descId: openDebugWindow
    branding:
      displayBrandingArea: true
    connectionTimeout: 60000
    showHelpButton: true
    effectiveConnection:
      - critical
      - danger
      - warning
    # Whether the fallback mechanism should be used
    # when the locale string is empty. If false, the empty
    # string will be returned.
    fallbackOnEmptyLocaleString: true
    disableWebsocketFallback: true
  externalVideoPlayer:
    enabled: true
  kurento:
    wsUrl: wss://pbmeetqa.policybazaar.com/bbb-webrtc-sfu
    # wsUrl: HOST
    cameraWsOptions:
      # Valid for video-provider. Time (ms) before its WS connection times out
      # and tries to reconnect.
      wsConnectionTimeout: 4000
      # maxRetries: max reconnection retries
      maxRetries: 7
      # debug: console trace logging for video-provider's ws
      debug: false
      heartbeat:
        interval: 15000
        delay: 3000
        reconnectOnFailure: true
    # Time in milis to wait for the browser to return a gUM call (used in video-preview)
    gUMTimeout: 100000
    # Controls whether ICE candidates should be signaled to bbb-webrtc-sfu.
    # Enable this if you want to use Kurento as the media server.
    signalCandidates: false
    # traceLogs: <Boolean> - enable trace logs in SFU peers
    traceLogs: false
    cameraTimeouts:
      # Base camera timeout: used as the camera *sharing* timeout and
      # as the minimum camera subscribe reconnection timeout
      baseTimeout: 30000
      # Max timeout: used as the max camera subscribe reconnection timeout. Each
      # subscribe reattempt increases the reconnection timer up to this
      maxTimeout: 60000
    screenshare:
      # Whether volume control should be allowed if screen sharing has audio
      enableVolumeControl: true
      # Experimental. True is the canonical behavior. Flip to false to reverse
      # the negotiation flow for subscribers.
      subscriberOffering: false
      # Experimental. Server wide configuration to choose which bbb-webrtc-sfu
      # media server adapter should be used for screen sharing.
      # Default is undefined, which means the default setting in bbb-webrtc-sfu
      # prevails (screenshareMediaServer).
      #mediaServer: Kurento
      bitrate: 1500
      mediaTimeouts:
        maxConnectionAttempts: 2
        # Base screen media timeout (send|recv) - first connections
        baseTimeout: 20000
        # Base screen media timeout (send|recv) - re-connections
        baseReconnectionTimeout: 8000
        # Max timeout: used as the max camera subscribe connection timeout. Each
        # subscribe reattempt increases the reconnection timer up to this
        maxTimeout: 25000
        timeoutIncreaseFactor: 1.5
      constraints:
        video:
          frameRate:
            ideal: 5
            max: 10
          width:
            max: 2560
          height:
            max: 1600
        audio: true
    # cameraProfiles is an array of:
    # - id: profile identifier
    #   name: human-readable profile name
    #   bitrate
    #   hidden: whether this profile will be hidden in the video preview dropdown
    #   constraints: a video media constraints dictionary (without the video key)
    cameraProfiles:
      # id: unique identifier of the profile
      # name: name of the profile visible to users
      # default: if this is the default profile which is pre-selected
      # bitrate: the average bitrate for used for a webcam stream
      # constraints:
      #   # Optional constraints put on the requested video a browser MAY honor
      #   # For a detailed list on possible values see:
      #   # https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackConstraints
      #   # Examples:
      #   width: requested width of the camera stream
      #   frameRate: requested framerate
      - id: low-u30
        name: low-u30
        bitrate: 30
        hidden: true
      - id: low-u25
        name: low-u25
        bitrate: 40
        hidden: true
      - id: low-u20
        name: low-u20
        bitrate: 50
        hidden: true
      - id: low-u15
        name: low-u15
        bitrate: 70
        hidden: true
      - id: low-u12
        name: low-u12
        bitrate: 90
        hidden: true
      - id: low-u8
        name: low-u8
        bitrate: 100
        hidden: true
      - id: low
        name: Low
        default: false
        bitrate: 100
      - id: medium
        name: Medium
        default: true
        bitrate: 200
      - id: high
        name: High
        default: false
        bitrate: 500
        constraints:
          width: 1280
          height: 720
          frameRate: 15
      - id: hd
        name: High definition
        default: false
        bitrate: 800
        constraints:
          width: 1280
          height: 720
          frameRate: 30
    enableScreensharing: true
    enableVideo: true
    enableVideoMenu: true
    enableVideoPin: true
    # Experimental. Server wide configuration to choose which bbb-webrtc-sfu
    # media server adapter should be used for listen only.
    # Default is undefined, which means the default setting in bbb-webrtc-sfu
    # prevails (listenOnlyMediaServer).
    #listenOnlyMediaServer: mediasoup
    # Experimental. Server wide configuration to choose which bbb-webrtc-sfu
    # media server adapter should be used for webcams.
    # Default is undefined, which means the default setting in bbb-webrtc-sfu
    # prevails (videoMediaServer).
    #videoMediaServer: Kurento
    autoShareWebcam: false
    skipVideoPreview: false
    skipVideoPreviewOnFirstJoin: false
    # cameraSortingModes.paginationSorting: sorting mode to be applied when pagination is active
    # cameraSortingModes.defaultSorting: sorting mode when pagination is not active (full mesh)
    # Current implemented modes are:
    # 'LOCAL_ALPHABETICAL' | 'VOICE_ACTIVITY_LOCAL' | 'LOCAL_VOICE_ACTIVITY' | 'LOCAL_PRESENTER_ALPHABETICAL'
    # The algorithm names are self-explanatory.
    cameraSortingModes:
      defaultSorting: LOCAL_ALPHABETICAL
      paginationSorting: VOICE_ACTIVITY_LOCAL
    # Entry `thresholds` is an array of:
    # - threshold: minimum number of cameras being shared for profile to applied
    #   profile: a camera profile id from the cameraProfiles configuration array
    #            that will be applied to all cameras when threshold is hit
    cameraQualityThresholds:
      enabled: true
      # applyConstraints: whether profile constraints should be applied on profile changes
      applyConstraints: false
      # privilegedStreams: whether cameras should revert to their original profile on
      # certain actions (eg floor changes, pin)
      privilegedStreams: true
      debounceTime: 2500
      thresholds:
        - threshold: 8
          profile: low-u8
        - threshold: 12
          profile: low-u12
        - threshold: 15
          profile: low-u15
        - threshold: 20
          profile: low-u20
        - threshold: 25
          profile: low-u25
        - threshold: 30
          profile: low-u30
    pagination:
      # WARNING: the pagination.enabled setting has moved to
      # public.app.defaultSettings.application.paginationEnabled
      # paginationToggleEnabled: show a pagination toggle in settings for the
      # user to enable/disable it
      paginationToggleEnabled: true
      # how long (in ms) the negotiation will be debounced after a page change.
      pageChangeDebounceTime: 2500
      # video page sizes for DESKTOP endpoints. It stands for the number of SUBSCRIBER streams.
      # PUBLISHERS aren't accounted for .
      # A page size of 0 (zero) means that the page size is unlimited (disabled).
      desktopPageSizes:
        moderator: 0
        viewer: 5
      # video page sizes for MOBILE endpoints
      mobilePageSizes:
        moderator: 2
        viewer: 2
    paginationThresholds:
      enabled: false
      thresholds:
        - users: 30
          desktopPageSizes:
            moderator: 25
            viewer: 25
        - users: 40
          desktopPageSizes:
            moderator: 20
            viewer: 20
        - users: 50
          desktopPageSizes:
            moderator: 16
            viewer: 16
        - users: 60
          desktopPageSizes:
            moderator: 14
            viewer: 12
        - users: 70
          desktopPageSizes:
            moderator: 12
            viewer: 10
        - users: 80
          desktopPageSizes:
            moderator: 10
            viewer: 8
        - users: 90
          desktopPageSizes:
            moderator: 8
            viewer: 6
        - users: 100
          desktopPageSizes:
            moderator: 6
            viewer: 4
  syncUsersWithConnectionManager:
    enabled: false
    syncInterval: 60000
  poll:
    enabled: true
    allowCustomResponseInput: true
    maxCustom: 5
    maxTypedAnswerLength: 45
    chatMessage: true
  captions:
    enabled: true
    id: captions
    dictation: false
    background: '#000000'
    font:
      color: '#ffffff'
      family: Calibri
      size: 24px
    lines: 2
    time: 5000
  chat:
    enabled: true
    itemsPerPage: 100
    timeBetweenFetchs: 1000
    enableSaveAndCopyPublicChat: true
    bufferChatInsertsMs: 0
    startClosed: false
    min_message_length: 1
    max_message_length: 5000
    grouping_messages_window: 10000
    type_system: SYSTEM_MESSAGE
    type_public: PUBLIC_ACCESS
    type_private: PRIVATE_ACCESS
    system_userid: SYSTEM_MESSAGE
    system_username: SYSTEM_MESSAGE
    public_id: public
    public_group_id: MAIN-PUBLIC-GROUP-CHAT
    public_userid: public_chat_userid
    public_username: public_chat_username
    storage_key: UNREAD_CHATS
    system_messages_keys:
      chat_clear: PUBLIC_CHAT_CLEAR
      chat_poll_result: PUBLIC_CHAT_POLL_RESULT
      chat_exported_presentation: PUBLIC_CHAT_EXPORTED_PRESENTATION
    typingIndicator:
      enabled: true
    moderatorChatEmphasized: true
    autoConvertEmoji: true
    emojiPicker:
      enable: false
      frequentEmojiSortOnClick: false
    # e.g.: disableEmojis: ['1F595','1F922']
    disableEmojis: []
  notes:
    enabled: true
    id: notes
    pinnable: true
  layout:
    hidePresentationOnJoin: false
    showParticipantsOnLogin: true
    showPushLayoutButton: true
    showPushLayoutToggle: true
  pads:
    url: ETHERPAD_HOST
    cookie:
      path: /
      sameSite: None
      secure: true
  media:
    audio:
      #
      #
      # Default bridge to be used by full audio mechanism.
      # This is the bridge's name as contained in 'bridges' array
      defaultFullAudioBridge: sipjs
      #
      #
      # Server wide configuration to choose which bbb-webrtc-sfu
      # media server adapter should be used for fullaudio.
      # Default is undefined, which means the default setting in bbb-webrtc-sfu
      # prevails (fullAudioMediaServer).
      #fullAudioMediaServer: mediasoup
      #
      #
      # Default bridge to be used by listen only mechanism.
      defaultListenOnlyBridge: fullaudio
      #
      #
      # Bridge array, here's where we list our bridges.
      # To add new bridges, simply add the corresponding .js file in
      # /imports/api/audio/client/bridge/ and add it to this list.
      #
      # Each bridge in this list, must have a name and path attribute.
      # The name is the desired name/string you can set for your bridge, while
      # the path specifies the file path, relative to
      # '/imports/api/audio/client' dir.
      #
      bridges:
        # name:  The name of the bridge
        - name: sipjs
          # path: the bridge file path, relative to /imports/api/audio/client
          path: 'bridge/sip'
        - name: fullaudio
          path: 'bridge/sfu-audio-bridge'
    stunTurnServersFetchAddress: '/bigbluebutton/api/stuns'
    cacheStunTurnServers: true
    fallbackStunServer: ''
    # Forces relay usage on all browsers, environments and media modules.
    # If true, supersedes public.kurento.forceRelayOnFirefox
    forceRelay: false
    # Firefox has a buggy ICE implementation. With mediasoup this leads to
    # connection problems unless all traffic is relayed through a turn server.
    forceRelayOnFirefox: true
    mediaTag: '#remote-media'
    callTransferTimeout: 5000
    callHangupTimeout: 2000
    callHangupMaximumRetries: 10
    echoTestNumber: 'echo'
    listenOnlyCallTimeout: 25000
    # Experimental. True is the canonical behavior. Flip to false to reverse
    # the negotiation flow for LO subscribers.
    listenOnlyOffering: false
    #Timeout (ms) for gathering ICE candidates. When this timeout expires
    #the SDP is sent to the server with the candidates the browser gathered
    #so far. Increasing this value might help avoiding 1004 error when
    #user activates microphone.
    iceGatheringTimeout: 500
    # Timeout (ms) for connecting to the audio's signaling websocket.
    audioConnectionTimeout: 5000
    # Delay (ms) between each reconnection attempt of the audio's signaling
    # websocket.
    audioReconnectionDelay: 1000
    # Number of reconnection attempts of the signaling websocket, before
    # showing to the user there's an audio error.
    audioReconnectionAttempts: 3
    sipjsHackViaWs: true
    # sipjsAllowMdns: whether mDNS candidates should be allowed in local SDPs.
    # Default is false since FreeSWITCH doesn't resolve mDNS by default.
    sipjsAllowMdns: false
    # the fqdn of this host.
    # If you run a traditional setup of multiple nodes behind scalelite and the users see the hostnames of the
    # individual nodes, you can leave this at the default setting
    sip_ws_host: ''
    # Mute/umute toggle throttle time
    toggleMuteThrottleTime: 300
    #Websocket keepAlive interval (seconds). You may set this to prevent
    #websocket disconnection in some environments. When set, BBB will send
    #'\r\n\r\n' string through SIP.js's websocket. If not set, default value
    #is 0.
    websocketKeepAliveInterval: 30
    #Debounce time (seconds) for sending SIP.js's websocket keep alive message.
    #If not set, default value is 10.
    websocketKeepAliveDebounce: 10
    #Trace sip/audio messages in browser. If not set, default value is false.
    traceSip: false
    # SDP semantics: plan-b|unified-plan
    sdpSemantics: 'unified-plan'
    # localEchoTest:
    #   enabled: Boolean => enables an experimental, simplified echo test mode
    #   initialHearingState: Boolean => whether users should hear themselves firsthand
    #   useRtcLoopbackInChromium: Boolean => whether a local RTC loopback should
    #   be used in Chromium browsers. Works around the fact that Chromium has no
    #   echo cancellation in non-rtc audio streams
    localEchoTest:
      enabled: true
      initialHearingState: true
      useRtcLoopbackInChromium: true
      # delay: delay (seconds) to be added to the audio feedback return
      delay:
        enabled: true
        delayTime: 0.5
        maxDelayTime: 2
    # showVolumeMeter: shows an energy bar for microphones in the AudioSettings view
    showVolumeMeter: true
    # networkPriorities: DSCP markings for each media type. Chromium only, applies
    # to sender flows. See https://datatracker.ietf.org/doc/html/rfc8837#section-5
    # for further info.
    #networkPriorities:
    #  audio: high
    #  webcam: medium
    #  screenshare: medium
  stats:
    enabled: true
    interval: 10000
    timeout: 30000
    log: true
    notification:
      warning: false
      error: true
    jitter:
      - 10
      - 20
      - 30
    loss:
      - 0.05
      - 0.1
      - 0.2
    rtt:
      - 500
      - 1000
      - 2000
    level:
      - warning
      - danger
      - critical
    help: STATS_HELP_URL
  presentation:
    allowDownloadable: true
    panZoomThrottle: 32
    restoreOnUpdate: false
    uploadEndpoint: '/bigbluebutton/presentation/upload'
    fileUploadConstraintsHint: false
    # mirroredFromBBBCore are values controlled in bbb-web properties file. We include a copy here for notification purposes
    mirroredFromBBBCore:
      uploadSizeMax: 30000000
      uploadPagesMax: 200
    uploadValidMimeTypes:
      - extension: .pdf
        mime: application/pdf
      - extension: .doc
        mime: application/msword
      - extension: .docx
        mime: application/vnd.openxmlformats-officedocument.wordprocessingml.document
      - extension: .xls
        mime: application/vnd.ms-excel
      - extension: .xlsx
        mime: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      - extension: .ppt
        mime: application/vnd.ms-powerpoint
      - extension: .pptx
        mime: application/vnd.openxmlformats-officedocument.presentationml.presentation
      - extension: .txt
        mime: text/plain
      - extension: .rtf
        mime: application/rtf
      - extension: .odt
        mime: application/vnd.oasis.opendocument.text
      - extension: .ods
        mime: application/vnd.oasis.opendocument.spreadsheet
      - extension: .odp
        mime: application/vnd.oasis.opendocument.presentation
      - extension: .odg
        mime: application/vnd.oasis.opendocument.graphics
      - extension: .odc
        mime: application/vnd.oasis.opendocument.chart
      - extension: .odi
        mime: application/vnd.oasis.opendocument.image
      - extension: .jpg
        mime: image/jpeg
      - extension: .jpeg
        mime: image/jpeg
      - extension: .png
        mime: image/png
  selectRandomUser:
    enabled: true
    countdown: false
  user:
    role_moderator: MODERATOR
    role_viewer: VIEWER
    label:
      moderator: false
      mobile: true
      guest: true
      sharingWebcam: true
  whiteboard:
    annotationsQueueProcessInterval: 60
    cursorInterval: 150
    maxStickyNoteLength: 1000
    # limit number of annotations per slide
    maxNumberOfAnnotations: 300
    annotations:
      status:
        start: DRAW_START
        update: DRAW_UPDATE
        end: DRAW_END
    styles:
      text:
        # Initial font family.
        # family: mono|sans|script|serif
        family: script
    toolbar:
      multiUserPenOnly: false
      colors:
        - label: black
          value: '#000000'
        - label: white
          value: '#ffffff'
        - label: red
          value: '#ff0000'
        - label: orange
          value: '#ff8800'
        - label: eletricLime
          value: '#ccff00'
        - label: Lime
          value: '#00ff00'
        - label: Cyan
          value: '#00ffff'
        - label: dodgerBlue
          value: '#0088ff'
        - label: blue
          value: '#0000ff'
        - label: violet
          value: '#8800ff'
        - label: magenta
          value: '#ff00ff'
        - label: silver
          value: '#c0c0c0'
      thickness:
        - value: 14
        - value: 12
        - value: 10
        - value: 8
        - value: 6
        - value: 4
        - value: 2
        - value: 1
      font_sizes:
        - value: 36
        - value: 32
        - value: 28
        - value: 24
        - value: 20
        - value: 16
      tools:
        - icon: text_tool
          value: text
        - icon: line_tool
          value: line
        - icon: circle_tool
          value: ellipse
        - icon: triangle_tool
          value: triangle
        - icon: rectangle_tool
          value: rectangle
        - icon: pen_tool
          value: pencil
        - icon: hand
          value: hand
      presenterTools:
        - text
        - line
        - ellipse
        - triangle
        - rectangle
        - pencil
        - hand
      multiUserTools:
        - text
        - line
        - ellipse
        - triangle
        - rectangle
        - pencil
  clientLog:
    server:
      enabled: false
      level: info
    console:
      enabled: true
      level: debug
    external:
      enabled: false
      level: info
      url: https://LOG_HOST/html5Log
      method: POST
      throttleInterval: 400
      flushOnClose: true
      logTag: ''
  virtualBackgrounds:
    enabled: true
    enableVirtualBackgroundUpload: false
    storedOnBBB: true
    showThumbnails: true
    imagesPath: /resources/images/virtual-backgrounds/
    thumbnailsPath: /resources/images/virtual-backgrounds/thumbnails/
    fileNames:
      - pb.jpg
      - pb2.jpg
private:
  analytics:
    includeChat: true
  app:
    host: 127.0.0.1
    localesUrl: /locale-list
    pencilChunkLength: 100
    loadSlidesFromHttpAlways: false
  redis:
    host: 127.0.0.1
    port: '6379'
    timeout: 5000
    password: null
    debug: false
    metrics:
      queueMetrics: false
      metricsDumpIntervalMs: 60000
      metricsFolderPath: METRICS_FOLDER
      removeMeetingOnEnd: true
    channels:
      toAkkaApps: to-akka-apps-redis-channel
      toThirdParty: to-third-party-redis-channel
    subscribeTo:
      - to-html5-redis-channel
      - from-akka-apps-[^f]*
      - from-third-party-redis-channel
    async:
      - from-akka-apps-wb-redis-channel
    ignored:
      - CheckAlivePongSysMsg
      - DoLatencyTracerMsg
  serverLog:
    level: info
    streamerLog: false
    includeServerInfo: true
    healthChecker:
      enable: true
      intervalMs: 30000
  minBrowserVersions:
    - browser: chrome
      version: 72
    - browser: chromeMobileIOS
      version: 94
    - browser: firefox
      version: 68
    - browser: firefoxMobile
      version: 68
    - browser: edge
      version: 79
    - browser: ie
      version: Infinity
    - browser: safari
      version: [12, 1]
    - browser: mobileSafari
      version: [12, 1]
    - browser: opera
      version: 50
    - browser: electron
      version: [0, 36]
    - browser: SamsungInternet
      version: 10
    - browser: YandexBrowser
      version: 19
  # Direct Prometheus instrumentation.
  # EXPERIMENTAL, so disabled by default.
  prometheus:
    enabled: false
    # Metrics endpoint path
    path: '/metrics'
    # Whether default metrics for Node.js processes should be exported
    collectDefaultMetrics: false
    # Whether redis metrics should be exported
    collectRedisMetrics: false
