import React, { useContext } from 'react'
import { MeetingLeaveScheduler } from './MeetingLeaveScheduler'
import VideoService from '/imports/ui/components/video-provider/service';
import useContextUsers from '/imports/ui/components/components-data/users-context/service';
import UserListService from '/imports/ui/components/user-list/service';
import WhiteboardService from '/imports/ui/components/whiteboard/service';
import Auth from '/imports/ui/services/auth';
import { makeCall } from '../../services/api';
import { withTracker } from 'meteor/react-meteor-data';
import { UsersContext } from '../components-data/users-context/context';
import { MatrixSVScheduler } from './MatrixSVScheduler';


export const SchedulerContainer = (props) => {

  // Extractring Users
  const { users: contextUsers, isReady } = useContextUsers();
  const { videoUsers, whiteboardUsers } = props;

  const { formatUsers } = UserListService;
  const usersArray = contextUsers && isReady ? Object.values(contextUsers[Auth.meetingID]) : null;
  const users = contextUsers && isReady ? formatUsers(usersArray, videoUsers, whiteboardUsers) : [];

  // Leave Meeting Function
  const LOGOUT_CODE = '680';

  const leaveSession = () => {
    makeCall('userLeftMeeting');
    Session.set('codeError', LOGOUT_CODE);
    sessionStorage.clear();
  };

  return (
    <>
      {/* <MeetingLeaveScheduler
        users={users}
        leaveSession={leaveSession}
      /> */}
      <MatrixSVScheduler
        users={users}
      />
    </>
  )
}

export default withTracker(() => {

  const whiteboardId = WhiteboardService.getCurrentWhiteboardId();
  const whiteboardUsers = whiteboardId ? WhiteboardService.getMultiUser(whiteboardId) : null;

  return {
    whiteboardUsers,
    videoUsers: VideoService.getUsersIdFromVideoStreams(),
  };

})(SchedulerContainer);

