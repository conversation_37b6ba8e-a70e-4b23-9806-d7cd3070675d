import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Session } from 'meteor/session';
import {
  defineMessages, injectIntl, FormattedMessage,
} from 'react-intl';
import Styled from './styles';
import PermissionsOverlay from '../permissions-overlay/component';
import AudioSettings from '../audio-settings/component';
import EchoTest from '../echo-test/component';
import Help from '../help/component';
import AudioDial from '../audio-dial/component';
import AudioAutoplayPrompt from '../autoplay/component';
import Settings from '/imports/ui/services/settings';
import CaptionsSelectContainer from '/imports/ui/components/audio/captions/select/container';
import { showModal } from '/imports/ui/components/common/modal/service';

import Service from '/imports/ui/components/audio/service.js'
import Auth from '/imports/ui/services/auth';
import Meetings from '/imports/api/meetings';

import '../../../../../client/stylesheets/audio-modal.css';
import '../../../../..//client/stylesheets/bbb-icons.css'
import AudioCarousel from './Carousel';
import LinearProgress from '@material-ui/core/LinearProgress';
import { makeCall } from '/imports/ui/services/api';
import UserListService from '/imports/ui/components/user-list/service';
import { getCookieValue } from '/imports/ui/services/storage/cookie';

const propTypes = {
  intl: PropTypes.shape({
    formatMessage: PropTypes.func.isRequired,
  }).isRequired,
  closeModal: PropTypes.func.isRequired,
  joinMicrophone: PropTypes.func.isRequired,
  joinListenOnly: PropTypes.func.isRequired,
  joinEchoTest: PropTypes.func.isRequired,
  exitAudio: PropTypes.func.isRequired,
  leaveEchoTest: PropTypes.func.isRequired,
  changeInputDevice: PropTypes.func.isRequired,
  changeOutputDevice: PropTypes.func.isRequired,
  isEchoTest: PropTypes.bool.isRequired,
  isConnecting: PropTypes.bool.isRequired,
  isConnected: PropTypes.bool.isRequired,
  isUsingAudio: PropTypes.bool.isRequired,
  inputDeviceId: PropTypes.string,
  outputDeviceId: PropTypes.string,
  formattedDialNum: PropTypes.string.isRequired,
  showPermissionsOvelay: PropTypes.bool.isRequired,
  listenOnlyMode: PropTypes.bool.isRequired,
  joinFullAudioImmediately: PropTypes.bool,
  forceListenOnlyAttendee: PropTypes.bool.isRequired,
  audioLocked: PropTypes.bool.isRequired,
  resolve: PropTypes.func,
  isMobileNative: PropTypes.bool.isRequired,
  isIE: PropTypes.bool.isRequired,
  formattedTelVoice: PropTypes.string.isRequired,
  autoplayBlocked: PropTypes.bool.isRequired,
  handleAllowAutoplay: PropTypes.func.isRequired,
  changeInputStream: PropTypes.func.isRequired,
  localEchoEnabled: PropTypes.bool.isRequired,
  showVolumeMeter: PropTypes.bool.isRequired,
  notify: PropTypes.func.isRequired,
  extId: PropTypes.string.isRequired,
};

const defaultProps = {
  inputDeviceId: null,
  outputDeviceId: null,
  resolve: null,
  joinFullAudioImmediately: false,
};

const intlMessages = defineMessages({
  microphoneLabel: {
    id: 'app.audioModal.microphoneLabel',
    description: 'Join mic audio button label',
  },
  listenOnlyLabel: {
    id: 'app.audioModal.listenOnlyLabel',
    description: 'Join listen only audio button label',
  },
  listenOnlyDesc: {
    id: 'app.audioModal.listenOnlyDesc',
    description: 'Join listen only audio button description',
  },
  microphoneDesc: {
    id: 'app.audioModal.microphoneDesc',
    description: 'Join mic audio button description',
  },
  closeLabel: {
    id: 'app.audioModal.closeLabel',
    description: 'close audio modal button label',
  },
  audioChoiceLabel: {
    id: 'app.audioModal.audioChoiceLabel',
    description: 'Join audio modal title',
  },
  audioJoinLabel: {
    id: 'app.audioModal.audioJoinLabel',
    description: 'Join audio modal title',
  },
  iOSError: {
    id: 'app.audioModal.iOSBrowser',
    description: 'Audio/Video Not supported warning',
  },
  iOSErrorDescription: {
    id: 'app.audioModal.iOSErrorDescription',
    description: 'Audio/Video not supported description',
  },
  iOSErrorRecommendation: {
    id: 'app.audioModal.iOSErrorRecommendation',
    description: 'Audio/Video recommended action',
  },
  echoTestTitle: {
    id: 'app.audioModal.echoTestTitle',
    description: 'Title for the echo test',
  },
  settingsTitle: {
    id: 'app.audioModal.settingsTitle',
    description: 'Title for the audio modal',
  },
  helpTitle: {
    id: 'app.audioModal.helpTitle',
    description: 'Title for the audio help',
  },
  audioDialTitle: {
    id: 'app.audioModal.audioDialTitle',
    description: 'Title for the audio dial',
  },
  connecting: {
    id: 'app.audioModal.connecting',
    description: 'Message for audio connecting',
  },
  ariaModalTitle: {
    id: 'app.audioModal.ariaTitle',
    description: 'aria label for modal title',
  },
  autoplayPromptTitle: {
    id: 'app.audioModal.autoplayBlockedDesc',
    description: 'Message for autoplay audio block',
  },
});

class AudioModal extends Component {
  constructor(props) {
    super(props);

    this.state = {
      content: null,
      hasError: false,
      errCode: null,
      showNetworkWarning: false,
    };

    this.startTime = null;
    this.networkWarningTimer = null;

    this.handleGoToAudioOptions = this.handleGoToAudioOptions.bind(this);
    this.handleGoToAudioSettings = this.handleGoToAudioSettings.bind(this);
    this.handleRetryGoToEchoTest = this.handleRetryGoToEchoTest.bind(this);
    this.handleGoToEchoTest = this.handleGoToEchoTest.bind(this);
    this.handleJoinMicrophone = this.handleJoinMicrophone.bind(this);
    this.handleJoinLocalEcho = this.handleJoinLocalEcho.bind(this);
    this.handleJoinListenOnly = this.handleJoinListenOnly.bind(this);
    this.skipAudioOptions = this.skipAudioOptions.bind(this);
    this.clearNetworkWarningTimer = this.clearNetworkWarningTimer.bind(this);

    this.contents = {
      echoTest: {
        title: intlMessages.echoTestTitle,
        component: () => this.renderEchoTest(),
      },
      settings: {
        title: intlMessages.settingsTitle,
        component: () => this.renderAudioSettings(),
      },
      help: {
        title: intlMessages.helpTitle,
        component: () => this.renderHelp(),
      },
      audioDial: {
        title: intlMessages.audioDialTitle,
        component: () => this.renderAudioDial(),
      },
      autoplayBlocked: {
        title: intlMessages.autoplayPromptTitle,
        component: () => this.renderAutoplayOverlay(),
      },
    };
    this.failedMediaElements = [];
  }

  componentDidMount() {
    const {
      forceListenOnlyAttendee,
      joinFullAudioImmediately,
      listenOnlyMode,
      audioLocked,
      isUsingAudio,
    } = this.props;
    window.addEventListener("CLOSE_AUDIO_MODAL", this.handleCloseAudioModal);


    this.startTime = new Date();

    // Set timer to show network warning after 5 seconds
    this.networkWarningTimer = setTimeout(() => {
      this.setState({ showNetworkWarning: true });
    }, 5000);

    if (Service.isUserModerator()) {
      // Setting hash in localStorage for rejoin (INBOUND VC)
      const agentSid = getCookieValue('agentSid');
      localStorage.setItem('hash', agentSid);
    }

    // const CallType = localStorage.getItem('CallType');

    // if (CallType) {
    //   if (CallType.toUpperCase() === "VIDEO" || CallType.toUpperCase() === "VIDEOCALL") {
    //     if (joinFullAudioImmediately) {
    //       this.handleJoinMicrophone();
    //     } else {
    //       this.handleGoToEchoTest();
    //     }
    //   } else if (CallType.toUpperCase() === "SCREENSHARE") {
    //     this.handleJoinListenOnly();
    //   }
      
    //   if (Service.isUserModerator()) {
    //     this.props.guestUsersCall(
    //       [...this.props.guestUsers, ...this.props.authenticatedUsers],
    //       "ALLOW"
    //     );
    //   }
      
    //   const roomId = this.props.extId
    //   logRoomEvents(roomId);
    // } else {
    //   if(!Service.isUserModerator()) { // if Customer
    //     return this.closeAudioModal();
    //   }
    // }

    if (Service.isUserModerator()) {
      
      this.props.guestUsersCall(
        [...this.props.guestUsers, ...this.props.authenticatedUsers],
        "ALLOW"
      );

    }

    if (!isUsingAudio) {
      if (forceListenOnlyAttendee || audioLocked) return this.handleJoinListenOnly();

      if (joinFullAudioImmediately) return this.handleJoinMicrophone();

      if (!listenOnlyMode) return this.handleGoToEchoTest();
    }
    return false;
  }

  componentDidUpdate(prevProps) {
    const { autoplayBlocked, closeModal } = this.props;

    if (autoplayBlocked !== prevProps.autoplayBlocked) {
      if (autoplayBlocked) {
        this.setContent({ content: 'autoplayBlocked' });
      } else {
        closeModal();
      }
    }
  }

  componentWillUnmount() {
    const {
      isEchoTest,
      exitAudio,
      resolve,
    } = this.props;

    const timeDifference = (new Date() - this.startTime) / 1000;

    localStorage.setItem('audioModalDuration', timeDifference);

    // Clear the network warning timer
    if (this.networkWarningTimer) {
      clearTimeout(this.networkWarningTimer);
    }

    if (isEchoTest) {
      exitAudio();
    }
    if (resolve) resolve();
    window.removeEventListener("CLOSE_AUDIO_MODAL", this.handleCloseAudioModal);
    Session.set('audioModalIsOpen', false);
  }

  clearNetworkWarningTimer() {
    if (this.networkWarningTimer) {
      clearTimeout(this.networkWarningTimer);
      this.networkWarningTimer = null;
    }
  }

  handleGoToAudioOptions() {
    this.setState({
      content: null,
      hasError: true,
      disableActions: false,
    });
  }

  handleGoToAudioSettings() {
    const { leaveEchoTest } = this.props;
    leaveEchoTest().then(() => {
      this.setState({
        content: 'settings',
      });
    });
  }

  handleRetryGoToEchoTest() {
    this.setState({
      hasError: false,
      content: null,
    });

    return this.handleGoToEchoTest();
  }

  handleGoToLocalEcho() {
    // Simplified echo test: this will return the AudioSettings with:
    //   - withEcho: true
    // Echo test will be local and done in the AudioSettings view instead of the
    // old E2E -> yes/no -> join view
    this.setState({
      content: 'settings',
    });
  }

  // Function for closing audio modal at customer end
  // After closing, customer will automatically in join audio/ listen only mode.
  closeAudioModal = () => {
    showModal(null);
    // After 0.5 second of modal close, mode will be decided based on agent mode
    setTimeout(() => {
      if(!Service.isUserModerator()){
        const {users, joinFullAudioImmediately } = this.props;
        for(let user of users) {
          if(user.role == "MODERATOR") {
            let voiceUser = UserListService.curatedVoiceUser(user.userId);
            if(voiceUser.isListenOnly) {
              this.handleJoinListenOnly();
              return
            } else {
              if (joinFullAudioImmediately) {
                return this.handleJoinMicrophone();
              } else {
                this.handleGoToEchoTest();
              }
              return
            }
          }
        }
      }
    },500)
  }

  handleCloseAudioModal = () => {
    showModal(null);
  }

  handleGoToEchoTest() {
    const { AudioError } = this.props;
    const { MIC_ERROR } = AudioError;
    const noSSL = !window.location.protocol.includes('https');

    if (noSSL) {
      return this.setState({
        content: 'help',
        errCode: MIC_ERROR.NO_SSL,
      });
    }

    const {
      joinEchoTest,
      isConnecting,
      localEchoEnabled,
    } = this.props;

    const {
      disableActions,
    } = this.state;

    if (disableActions && isConnecting) return null;

    if (localEchoEnabled) return this.handleGoToLocalEcho();

    this.setState({
      hasError: false,
      disableActions: true,
    });

    return joinEchoTest().then(() => {
      this.setState({
        content: 'echoTest',
        disableActions: false,
      });
    }).catch((err) => {
      this.handleJoinMicrophoneError(err);
    });
  }

  handleJoinListenOnly() {
    const {
      joinListenOnly,
      isConnecting,
    } = this.props;

    const {
      disableActions,
    } = this.state;

    if (disableActions && isConnecting) return null;

    this.setState({
      disableActions: true,
    });

    joinListenOnly().then(() => {
      // Clear network warning timer on successful connection
      this.clearNetworkWarningTimer();

      this.setState({
        disableActions: false,
      });
      localStorage.setItem("CallType", "SCREENSHARE")
      window.dispatchEvent(new Event("CLOSE_AUDIO_MODAL"));

    }).catch((err) => {
      if (err.type === 'MEDIA_ERROR') {
        this.setState({
          content: 'help',
        });
      }
    });
  }

  handleJoinLocalEcho(inputStream) {
    const { changeInputStream } = this.props;
    // Reset the modal to a connecting state - this kind of sucks?
    // prlanzarin Apr 04 2022
    this.setState({
      content: null,
    });
    if (inputStream) changeInputStream(inputStream);
    this.handleJoinMicrophone();
  }

  handleJoinMicrophone() {
    const {
      joinMicrophone,
      isConnecting,
      recordingStatus,
    } = this.props;

    const {
      disableActions,
    } = this.state;

    if (disableActions && isConnecting) return;

    this.setState({
      hasError: false,
      disableActions: true,
    });

    joinMicrophone().then(() => {
      // Clear network warning timer on successful connection
      this.clearNetworkWarningTimer();

      this.setState({
        disableActions: false,
      });
      if(!Service.isUserModerator()){
        localStorage.setItem("CallType", "VIDEOCALL")
      }

      const meetingId = Auth.meetingID;
      const meetingObject = Meetings.findOne({
        meetingId,
      }, { fields: { 'meetingProp.extId': 1 } });

      const eventAction = "JOINED_SUCCESSFULLY"
      const details = {
        meetingId: meetingObject.meetingProp.extId,
        error: null,
        ts: new Date().toString(),
      }
      gaEventTracker(eventAction, details, 0, isAgent);

      window.dispatchEvent(new Event("CLOSE_AUDIO_MODAL"));
      if(!recordingStatus){
        makeCall('toggleRecording')
      }
    }).catch((err) => {
      this.handleJoinMicrophoneError(err);
    });
  }

  handleJoinMicrophoneError(err) {
    const { type } = err;
    switch (type) {
      case 'MEDIA_ERROR':
        this.setState({
          content: 'help',
          errCode: 0,
          disableActions: false,
        });
        break;
      case 'CONNECTION_ERROR':
      default:
        this.setState({
          errCode: 0,
          disableActions: false,
        });
        break;
    }
  }

  setContent(content) {
    this.setState(content);
  }

  skipAudioOptions() {
    const {
      isConnecting,
    } = this.props;

    const {
      content,
      hasError,
    } = this.state;

    return isConnecting && !content && !hasError;
  }

  moderatorAudioDialogBox() {
    const { joinFullAudioImmediately } = this.props;

    return (
      <div className='main-modal'>
        {/* Header */}
        <div className='join-header'>
          <p className="header-text">There is <b> 5x</b> possibility of <b>conversion </b>for leads using <b> Presentation/Screen Sharing </b> <br /> <br />Ask your customer to connect over <b> PBMeet</b></p><br /><br />
        </div>

        {/* Screenshare Button */}
        <div className='screenshare-button'>
          <button 
            className='join-button' 
            onClick={() => {
              this.handleJoinListenOnly()

              this.props.guestUsersCall(
                [...this.props.guestUsers].concat(this.props.authenticatedUsers),
                  "ALLOW",
              )
            }
            }>
              <i className='icon-bbb-desktop join-icon' ></i>Share Screen with Customer</button>
        </div>

        <br /> <br />

        {/* Video Button */}
        <div className='video-button'>
          <button className='join-button' onClick= {() => {
            joinFullAudioImmediately
              ? this.handleJoinMicrophone()
              : this.handleGoToEchoTest()

            this.props.guestUsersCall(
              [...this.props.guestUsers].concat(this.props.authenticatedUsers),
                "ALLOW",
            )
          }
          }>
            <i className='icon-bbb-video join-icon' ></i>Start Video Call</button>
        </div>
      </div>
    )
  }

  customerAudioDialogBox() {
    const {
      intl,
      listenOnlyMode,
      forceListenOnlyAttendee,
      joinFullAudioImmediately,
      audioLocked,
      isMobileNative,
      formattedDialNum,
      isRTL,
    } = this.props;

    const showMicrophone = forceListenOnlyAttendee || audioLocked;
    const arrow = isRTL ? '←' : '→';
    const dialAudioLabel = `${intl.formatMessage(intlMessages.audioDialTitle)} ${arrow}`;

    return (
      <div>
        <Styled.AudioOptions data-test="audioModalOptions">
          {!showMicrophone && !isMobileNative
              && (
              <>
                <Styled.AudioModalButton
                  label={intl.formatMessage(intlMessages.microphoneLabel)}
                  data-test="microphoneBtn"
                  aria-describedby="mic-description"
                  icon="unmute"
                  circle
                  size="jumbo"
                  disabled={audioLocked}
                  onClick= {() => {
                    
                    joinFullAudioImmediately
                      ? this.handleJoinMicrophone()
                      : this.handleGoToEchoTest()

                    this.props.guestUsersCall(
                      [...this.props.guestUsers].concat(this.props.authenticatedUsers),
                      "ALLOW",
                    )
                    }
                  }
                />
                <span className="sr-only" id="mic-description">
                  {intl.formatMessage(intlMessages.microphoneDesc)}
                </span>
              </>
              )}
          {listenOnlyMode
              && (
              <>
                <Styled.AudioModalButton
                  label={intl.formatMessage(intlMessages.listenOnlyLabel)}
                  data-test="listenOnlyBtn"
                  aria-describedby="listenOnly-description"
                  icon="listen"
                  circle
                  size="jumbo"
                  onClick={() => {
                    this.handleJoinListenOnly()
      
                    this.props.guestUsersCall(
                      [...this.props.guestUsers].concat(this.props.authenticatedUsers),
                        "ALLOW",
                    )
                  }
                  }
                />
                <span className="sr-only" id="listenOnly-description">
                  {intl.formatMessage(intlMessages.listenOnlyDesc)}
                </span>
              </>
              )}
        </Styled.AudioOptions>
        {formattedDialNum ? (
          <Styled.AudioDial
            label={dialAudioLabel}
            size="md"
            color="secondary"
            onClick={() => {
              this.setState({
                content: 'audioDial',
              });
            }}
          />
        ) : null}
        <CaptionsSelectContainer />
      </div>
    );
  }

  findSourceAndProcess() {
    const meetingId = Auth.meetingID;
    const meetingObject = Meetings.findOne({
      meetingId,
    }, { fields: { 'meetingProp.extId': 1 } });
    
    let source = "";
    let process = "";

    if(meetingObject){
      const extId = meetingObject.meetingProp?.extId;
      if(extId){
        const arr = extId.split('_')
        if(arr.length > 0){
          source = arr[1];
          process = arr[2];
        }
      }
    }
    
    return {source: source, process: process};
  }

  renderAudioOptions() {
    const {
      intl,
      forceListenOnlyAttendee,
      joinFullAudioImmediately,
      audioLocked,
      isMobileNative,
      formattedDialNum,
      isRTL,
    } = this.props;

    const showMicrophone = forceListenOnlyAttendee || audioLocked;

    const arrow = isRTL ? '←' : '→';
    const dialAudioLabel = `${intl.formatMessage(intlMessages.audioDialTitle)} ${arrow}`;

    const {source, process} = this.findSourceAndProcess();

    if(source?.toUpperCase() == "BMS" && process?.toUpperCase() == "VERIFICATION"){
      if(Service.isUserModerator()){
        return (
          <div className='main-modal'>
            {/* Header */}
            <div className='join-header'>
              <p className="header-text">There is <b> 5x</b> possibility of <b>conversion </b>for leads using <b> Presentation/Screen Sharing </b> <br /> <br />Ask your customer to connect over <b> PBMeet</b></p><br /><br />
            </div>
  
            {/* Video Button */}
            <div className='video-button'>
              <button className='join-button' onClick= {() => {
                joinFullAudioImmediately
                  ? this.handleJoinMicrophone()
                  : this.handleGoToEchoTest()
    
                this.props.guestUsersCall(
                  [...this.props.guestUsers].concat(this.props.authenticatedUsers),
                    "ALLOW",
                )
              }
              }>
                <i className='icon-bbb-video join-icon' ></i>Start Video Call</button>
            </div>
          </div>
        )
      }
      else {
        return (
          <div>
            <Styled.AudioOptions data-test="audioModalOptions">
              {!showMicrophone && !isMobileNative
                && (
                <>
                  <Styled.AudioModalButton
                    label={intl.formatMessage(intlMessages.microphoneLabel)}
                    data-test="microphoneBtn"
                    aria-describedby="mic-description"
                    icon="unmute"
                    circle
                    size="jumbo"
                    disabled={audioLocked}
                    onClick= {() => {
                      
                      joinFullAudioImmediately
                        ? this.handleJoinMicrophone()
                        : this.handleGoToEchoTest()
                      }
                    }
                  />
                  <span className="sr-only" id="mic-description">
                    {intl.formatMessage(intlMessages.microphoneDesc)}
                  </span>
                </>
              )}
            </Styled.AudioOptions>
            {formattedDialNum ? (
              <Styled.AudioDial
                label={dialAudioLabel}
                size="md"
                color="secondary"
                onClick={() => {
                  this.setState({
                    content: 'audioDial',
                  });
                }}
              />
            ) : null}
            <CaptionsSelectContainer />
          </div>
        );
      }
    } else {
      return (
        <Styled.AudioModalContent data-test="audioModalContent">
          <LinearProgress style={{width: '100%'}} />
          {this.state.showNetworkWarning && (
            <div style={{
              color: '#d32f2f',
              textAlign: 'center',
              fontSize: '14px',
              fontWeight: '500',
              margin: '16px 0',
              padding: '12px',
              backgroundColor: '#ffebee',
              border: '1px solid #ffcdd2',
              borderRadius: '4px'
            }}>
              Connection taking longer than expected? If you're on an office network,
              please try switching to a personal network for better connectivity.
            </div>
          )}
          <h2>Setting up your room...</h2>
          <hr style={{width: '80%'}}></hr>
          <h3 style={{width: '80%', textAlign: 'center'}}>What makes Policybazaar one of India's favourite places to buy insurance?</h3>
          <AudioCarousel />
        </Styled.AudioModalContent>
      )
    }
  }

  renderContent() {
    const {
      isEchoTest,
      intl,
    } = this.props;

    const { content } = this.state;
    const { animations } = Settings.application;

    if (this.skipAudioOptions()) {
      return (
        <Styled.Connecting role="alert">
          <span data-test={!isEchoTest ? 'establishingAudioLabel' : 'connectingToEchoTest'}>
            {intl.formatMessage(intlMessages.connecting)}
          </span>
          <Styled.ConnectingAnimation animations={animations} />
        </Styled.Connecting>
      );
    }
    return content ? this.contents[content].component() : this.renderAudioOptions();
  }

  renderEchoTest() {
    return (
      <EchoTest
        handleNo={this.handleGoToAudioSettings}
        handleYes={this.handleJoinMicrophone}
      />
    );
  }

  renderAudioSettings() {
    const {
      isConnecting,
      isConnected,
      isEchoTest,
      inputDeviceId,
      outputDeviceId,
      joinEchoTest,
      changeInputDevice,
      changeOutputDevice,
      localEchoEnabled,
      showVolumeMeter,
      notify,
    } = this.props;

    const confirmationCallback = !localEchoEnabled
      ? this.handleRetryGoToEchoTest
      : this.handleJoinLocalEcho;

    const handleGUMFailure = () => {
      this.setState({
        content: 'help',
        errCode: 0,
        disableActions: false,
      });
    };

    return (
      <AudioSettings
        handleBack={this.handleGoToAudioOptions}
        handleConfirmation={confirmationCallback}
        handleGUMFailure={handleGUMFailure}
        joinEchoTest={joinEchoTest}
        changeInputDevice={changeInputDevice}
        changeOutputDevice={changeOutputDevice}
        isConnecting={isConnecting}
        isConnected={isConnected}
        isEchoTest={isEchoTest}
        inputDeviceId={inputDeviceId}
        outputDeviceId={outputDeviceId}
        withVolumeMeter={showVolumeMeter}
        withEcho={localEchoEnabled}
        produceStreams={localEchoEnabled || showVolumeMeter}
        notify={notify}
      />
    );
  }

  renderHelp() {
    const { errCode } = this.state;
    const { AudioError } = this.props;

    const audioErr = {
      ...AudioError,
      code: errCode,
    };

    const isAgent = Service.isUserModerator()

    return (
      <Help
        handleBack={this.handleGoToAudioOptions}
        audioErr={audioErr}
        isAgent={isAgent}
      />
    );
  }

  renderAudioDial() {
    const { formattedDialNum, formattedTelVoice } = this.props;
    return (
      <AudioDial
        formattedDialNum={formattedDialNum}
        telVoice={formattedTelVoice}
        handleBack={this.handleGoToAudioOptions}
      />
    );
  }

  renderAutoplayOverlay() {
    const { handleAllowAutoplay } = this.props;
    return (
      <AudioAutoplayPrompt
        handleAllowAutoplay={handleAllowAutoplay}
      />
    );
  }

  render() {
    const {
      intl,
      showPermissionsOvelay,
      closeModal,
      isIE,
    } = this.props;

    const { content } = this.state;

    const {source, process} = this.findSourceAndProcess();

    return (
      <span>
        {showPermissionsOvelay ? <PermissionsOverlay closeModal={closeModal} /> : null}

        <Styled.AudioModal
          onRequestClose={closeModal}
          data-test="audioModal"
          contentLabel={intl.formatMessage(intlMessages.ariaModalTitle)}
          shouldCloseOnOverlayClick={false}
          shouldCloseOnEsc={false}
          overlayClassName={"modalOverlay audioModalOverlay"}
          // title={intl.formatMessage(intlMessages.connecting)}
          // title={ !Service.isUserModerator() ? (
          //   !this.skipAudioOptions()
          //     ? (
          //       content
          //         ? intl.formatMessage(this.contents[content].title)
          //         : (source?.toUpperCase() == "BMS" && process?.toUpperCase() == "VERIFICATION" 
          //             ? intl.formatMessage(intlMessages.audioJoinLabel) 
          //             : intl.formatMessage(intlMessages.audioChoiceLabel)
          //           )
          //       )
          //     : null ) : null
          // }
        >
          {isIE ? (
            <Styled.BrowserWarning>
              <FormattedMessage
                id="app.audioModal.unsupportedBrowserLabel"
                description="Warning when someone joins with a browser that isnt supported"
                values={{
                  0: <a href="https://www.google.com/chrome/">Chrome</a>,
                  1: <a href="https://getfirefox.com">Firefox</a>,
                }}
              />
            </Styled.BrowserWarning>
          ) : null}
          <Styled.Content>
            {this.renderContent()}
          </Styled.Content>
        </Styled.AudioModal>
      </span>
    );
  }
}

AudioModal.propTypes = propTypes;
AudioModal.defaultProps = defaultProps;

export default injectIntl(AudioModal);
