/**
 ** BigBlueButton open source conferencing system - http://www.bigbluebutton.org/
 *
 * Copyright (c) 2012 BigBlueButton Inc. and by respective authors (see below).
 *
 * This program is free software; you can redistribute it and/or modify it under the
 * terms of the GNU Lesser General Public License as published by the Free Software
 * Foundation; either version 3.0 of the License, or (at your option) any later
 * version.
 *
 * BigBlueButton is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR A
 * PARTICULAR PURPOSE. See the GNU Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License along
 * with BigBlueButton; if not, see <http://www.gnu.org/licenses/>.
 *
 */
package org.bigbluebutton.web.controllers

import com.google.gson.Gson
import grails.web.context.ServletContextHolder
import groovy.json.JsonBuilder
import org.apache.commons.codec.binary.Base64
import org.apache.commons.codec.digest.DigestUtils
import org.apache.commons.io.FilenameUtils
import org.apache.commons.lang.RandomStringUtils
import org.apache.commons.lang.StringUtils
import org.bigbluebutton.api.*
import org.bigbluebutton.api.domain.GuestPolicy
import org.bigbluebutton.api.domain.Meeting
import org.bigbluebutton.api.domain.UserSession
import org.bigbluebutton.api.service.ValidationService
import org.bigbluebutton.api.service.ServiceUtils
import org.bigbluebutton.api.util.ParamsUtil
import org.bigbluebutton.api.util.ResponseBuilder
import org.bigbluebutton.presentation.PresentationUrlDownloadService
import org.bigbluebutton.presentation.UploadedPresentation
import org.bigbluebutton.presentation.SupportedFileTypes;
import org.bigbluebutton.web.services.PresentationService
import org.bigbluebutton.web.services.turn.StunTurnService
import org.bigbluebutton.web.services.turn.TurnEntry
import org.bigbluebutton.web.services.turn.StunServer
import org.bigbluebutton.web.services.turn.RemoteIceCandidate
import org.json.JSONArray
import groovy.json.JsonOutput

import javax.servlet.ServletRequest
import javax.servlet.http.Cookie
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.bigbluebutton.api.service.ExternalAPI
import org.bigbluebutton.configs.Config
import org.bigbluebutton.configs.PBMeetV2ConfigService
import org.bigbluebutton.api.util.MeetingDataCache
import java.text.SimpleDateFormat
import java.util.Date
import java.util.TimeZone
import java.math.BigInteger
import java.time.LocalDateTime;
import java.time.LocalTime
import java.time.Instant
import java.util.Base64;

import groovy.json.JsonSlurper
import groovy.json.JsonOutput

public class CookieManager {

    // public static void setItem(String name, String value, String path, HttpServletResponse response) {
    //     Cookie cookie = new Cookie(name, value);
    //     cookie.setPath(path);
    //     response.addCookie(cookie);
    // }

    public static String getItem(String name, HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(name)) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }

    // public static void removeItem(String name, HttpServletResponse response) {
    //     Cookie cookie = new Cookie(name, "");
    //     cookie.setMaxAge(0);
    //     response.addCookie(cookie);
    // }
}


class ApiController {
  private static final String CONTROLLER_NAME = 'ApiController'
  protected static final String RESP_CODE_SUCCESS = 'SUCCESS'
  protected static final String RESP_CODE_FAILED = 'FAILED'
  private static final String ROLE_MODERATOR = "MODERATOR"
  private static final String ROLE_ATTENDEE = "VIEWER"
  protected static Boolean REDIRECT_RESPONSE = true

  MeetingService meetingService;
  PresentationService presentationService
  ParamsProcessorUtil paramsProcessorUtil
  ClientConfigService configService
  PresentationUrlDownloadService presDownloadService
  StunTurnService stunTurnService
  HTML5LoadBalancingService html5LoadBalancingService
  ResponseBuilder responseBuilder = initResponseBuilder()
  ValidationService validationService

  def initResponseBuilder = {
    String protocol = this.getClass().getResource("").getProtocol();
    if (Objects.equals(protocol, "jar")) {
      // Application running inside a JAR file
      responseBuilder = new ResponseBuilder(getClass().getClassLoader(), "/WEB-INF/freemarker")
    } else if (Objects.equals(protocol, "file")) {
      // Application unzipped and running outside a JAR file
      String templateLoc = ServletContextHolder.servletContext.getRealPath("/WEB-INF/freemarker")
      // We should never have a null `templateLoc`
      responseBuilder = new ResponseBuilder(new File(templateLoc))
    }
  }

  /* general methods */
  def index = {
    log.debug CONTROLLER_NAME + "#index"
    response.addHeader("Cache-Control", "no-cache")

    withFormat {
      xml {
        render(text: responseBuilder.buildMeetingVersion(paramsProcessorUtil.getApiVersion(), paramsProcessorUtil.getBbbVersion(), RESP_CODE_SUCCESS), contentType: "text/xml")
      }
    }
  }

  /***********************************
   * CREATE (API)
   ***********************************/
  def create = {
    String API_CALL = 'create'
    log.debug CONTROLLER_NAME + "#${API_CALL}"
    // log.debug request.getParameterMap().toMapString()
    // log.debug request.getQueryString()

    String[] ap = request.getParameterMap().get("attendeePW")
    String attendeePW
    if(ap == null) log.info("No attendeePW provided")
    else attendeePW = ap[0]

    String[] mp = request.getParameterMap().get("moderatorPW")
    String moderatorPW
    if(mp == null) log.info("No moderatorPW provided")
    else moderatorPW = mp[0]

    // log.info("attendeePW [${attendeePW}]")
    // log.info("moderatorPW [${moderatorPW}]")

    if(attendeePW.equals("")) log.info("attendeePW is empty")
    if(moderatorPW.equals("")) log.info("moderatorPW is empty")

    String leadId = null;
    String[] arr = request.getParameterMap().get("leadID");
    if(arr != null) {
      leadId = arr[0]
    }

    Map.Entry<String, String> validationResponse = validateRequest(
            ValidationService.ApiCall.CREATE,
            request.getParameterMap(),
            request.getQueryString()
    )

    if(!(validationResponse == null)) {
      invalid(validationResponse.getKey(), validationResponse.getValue())
      return
    } else if (ParamsUtil.sanitizeString(params.meetingID) != params.meetingID) {
      invalid("validationError", "Invalid meeting ID")
      return
    }

    MeetingDataCache.addRoomDataToCache(params.meetingID, [
      "leadId": params.leadID,
      "agentId": params.agentID,
      "agentName": params.agentName,
      "customerName": params.customerName,
      "process": params.process,
      "createdOn": LocalDateTime.now(),
    ])

    // Ensure unique TelVoice. Uniqueness is not guaranteed by paramsProcessorUtil.
    if (!params.voiceBridge) {
      // Try up to 10 times. We should find a valid telVoice quickly unless
      // the server hosts ~100k meetings (default 5-digit telVoice)
      for (int i in 1..10) {
        String telVoice = paramsProcessorUtil.processTelVoice("");
        if (!meetingService.getNotEndedMeetingWithTelVoice(telVoice)) {
          params.voiceBridge = telVoice;
          break;
        }
      }
      // Still no unique voiceBridge found? Let createMeeting handle it.
    }

    params.html5InstanceId = html5LoadBalancingService.findSuitableHTML5ProcessByRoundRobin().toString()

    Meeting newMeeting = paramsProcessorUtil.processCreateParams(params)

    ApiErrors errors = new ApiErrors()

    if (meetingService.createMeeting(newMeeting)) {
      // See if the request came with pre-uploading of presentation.
      uploadDocuments(newMeeting, false);  //
      respondWithConference(newMeeting, null, null)
    } else {
      // Translate the external meeting id into an internal meeting id.
      String internalMeetingId = paramsProcessorUtil.convertToInternalMeetingId(params.meetingID);
      Meeting existing = meetingService.getNotEndedMeetingWithId(internalMeetingId);
      if (existing != null) {

        if(existing.getCustomerName().equals("Guest")){
          existing.setCustomerName(params.customerName);
        }

        log.debug "Existing conference found"
        Map<String, Object> updateParams = paramsProcessorUtil.processUpdateCreateParams(params);
        if (existing.getViewerPassword().equals(params.get("attendeePW")) && existing.getModeratorPassword().equals(params.get("moderatorPW"))) {
          //paramsProcessorUtil.updateMeeting(updateParams, existing);
          // trying to create a conference a second time, return success, but give extra info
          // Ignore pre-uploaded presentations. We only allow uploading of presentation once.
          //uploadDocuments(existing);
          respondWithConference(existing, "duplicateWarning", "This conference was already in existence and may currently be in progress.");
        } else {
          // BEGIN - backward compatibility
          invalid("idNotUnique", "A meeting already exists with that meeting ID.  Please use a different meeting ID.");
          return
          // END - backward compatibility

          // enforce meetingID unique-ness
          errors.nonUniqueMeetingIdError()
          respondWithErrors(errors)
        }

        return
      } else {
        Meeting existingTelVoice = meetingService.getNotEndedMeetingWithTelVoice(newMeeting.getTelVoice());
        Meeting existingWebVoice = meetingService.getNotEndedMeetingWithWebVoice(newMeeting.getWebVoice());
        if (existingTelVoice != null || existingWebVoice != null) {
          log.error "VoiceBridge already in use by another meeting (different meetingId)"
          errors.nonUniqueVoiceBridgeError()
          def key = "VoiceBridge_Already_In_Use"
          def msg = "VoiceBridge already in use by another meeting (different meetingId)"
          respondWithErrors(errors, key, msg)
        }
      }
    }

    String productId = getProductId(leadId);
    if (productId) {
      int prodId = productId.toInteger()
      newMeeting.setProductId(prodId);
    }
  }

  def getProductId(String leadId) {
    String url = "pbmeet/v2/GetLeadBasicInfo?leadId=" + leadId 

    try {
        ExternalAPI api = new ExternalAPI.ExternalAPIBuilder()
            .addMethod("GET")
            .addService("pbmeetApi")
            .build(); 

        def response = api.CALL_API(url);

        if (response == null) {
            log.info("### ${leadId} => GetLeadBasicInfo => response is null");
            return "0";
        }

        def responseObj = new groovy.json.JsonSlurper().parseText(response)
        log.info("### ${leadId} => GetLeadBasicInfo => ${responseObj}");
        return responseObj.ProductID;

    } catch (Exception e) {
        log.info("### ${leadId}: ERROR=> GetLeadBasicInfo => ${e.message}");
        return "0";
    }
  }


  def isIpAddressSame(Meeting meeting, String clientIp, boolean guest) {
    log.debug "Client IP: $clientIp"

    def targetList = (guest == false) ? meeting.getCustomerIPs() : meeting.getAdvisorIPs()

    log.debug "Target list: $targetList"

    if (targetList.contains(clientIp)) {
        def redirectFlag = false
        return true;
    }
    return false;
  }


  /**********************************************
   * JOIN API
   *********************************************/
  def join = {
    String API_CALL = 'join'
    log.debug CONTROLLER_NAME + "#${API_CALL}"
    // log.debug request.getParameterMap().toMapString()
    // log.debug request.getQueryString()

    Map.Entry<String, String> validationResponse = validateRequest(
            ValidationService.ApiCall.JOIN,
            request.getParameterMap(),
            request.getQueryString()
    )

    HashMap<String, String> roles = new HashMap<String, String>();

    roles.put("moderator", ROLE_MODERATOR);
    roles.put("viewer", ROLE_ATTENDEE);

    if(!(validationResponse == null)) {
      def redirectFlag = false;
      invalid(validationResponse.getKey(), validationResponse.getValue(), redirectFlag)
      return
    }

    Boolean authenticated = false;

    Boolean guest = false;
    if (!StringUtils.isEmpty(params.guest)) {
      guest = Boolean.parseBoolean(params.guest)
    } else {
      // guest param has not been passed. Make user as
      // authenticated by default. (ralam july 3, 2018)
      authenticated = true
    }


    if (!StringUtils.isEmpty(params.auth)) {
      authenticated = Boolean.parseBoolean(params.auth)
    }

    String fullName = ParamsUtil.stripControlChars(params.fullName)

    String attPW = params.password

    Meeting meeting = null;

    def requestURL = request.getRequestURL().toString()
    def queryString = request.getQueryString()
    def requestURLWithQuery = requestURL + (queryString ? "?" + queryString : "")

    String clientIp = request.getHeader('X-Forwarded-For')  // Getting Request from nginx so need to get X-Forwarded-For

    // log.info "URL IS: $requestURLWithQuery"

    meeting = ServiceUtils.findMeetingFromMeetingID(params.meetingID)

    // the createTime mismatch with meeting's createTime, complain
    // In the future, the createTime param will be required
    if (params.createTime != null) {
      long createTime = 0;
      try {
        createTime = Long.parseLong(params.createTime);
      } catch (Exception e) {
        log.warn("could not parse createTime param");
        createTime = -1;
      }
      if (createTime != meeting.getCreateTime()) {
        // BEGIN - backward compatibility
        invalid("mismatchCreateTimeParam", "The createTime parameter submitted mismatches with the current meeting.", REDIRECT_RESPONSE);
        return
        // END - backward compatibility

        errors.mismatchCreateTimeParam();
        respondWithErrors(errors, REDIRECT_RESPONSE);
        return
      }
    }

    // Now determine if this user is a moderator or a viewer.
    String role = null;

    // First Case: send a valid role
    if (!StringUtils.isEmpty(params.role) && roles.containsKey(params.role.toLowerCase())) {
      role = roles.get(params.role.toLowerCase());

    // Second case: role is not present or valid BUT there is password
    } else if (attPW != null && !attPW.isEmpty()){
      // Check if the meeting has passwords
      if ((meeting.getModeratorPassword() != null && !meeting.getModeratorPassword().isEmpty())
              && (meeting.getViewerPassword() != null && !meeting.getViewerPassword().isEmpty())){
        // Check which role does the user belong
        if (meeting.getModeratorPassword().equals(attPW)) {
          role = Meeting.ROLE_MODERATOR
        } else if (meeting.getViewerPassword().equals(attPW)) {
          role = Meeting.ROLE_ATTENDEE
        } else {
          log.debug("Password does not match any of the registered ones");
          response.addHeader("Cache-Control", "no-cache")
          withFormat {
            xml {
              render(text: responseBuilder.buildError("Params required", "You must enter a valid password",
                      RESP_CODE_FAILED), contentType: "text/xml")
            }
          }
          return
        }
      // In this case, the meeting doesn't have any password registered and there is no role param
      } else {
        log.debug("This meeting doesn't have any password");
        response.addHeader("Cache-Control", "no-cache")
        withFormat {
          xml {
            render(text: responseBuilder.buildError("Params required", "You must send the 'role' parameter, since " +
                    "this meeting doesn't have any password.", RESP_CODE_FAILED), contentType: "text/xml")
          }
        }
        return
      }

    // Third case: No valid role + no valid password
    } else {
      log.debug("No matching params encountered");
      response.addHeader("Cache-Control", "no-cache")
      withFormat {
        xml {
          render(text: responseBuilder.buildError("Params required", "You must either send the valid role of the user, or " +
                  "the password, sould the meeting has one.", RESP_CODE_FAILED), contentType: "text/xml")
        }
      }
      return
    }

    if (role.equals("MODERATOR")) {
      def userCookie = getUserCookie();
      meeting.setDataFromUserCookie(userCookie);
    }

    // We preprend "w_" to our internal meeting Id to indicate that this is a web user.
    // For users joining using the phone, we will prepend "v_" so it will be easier
    // to distinguish users who doesn't have a web client. (ralam june 12, 2017)
    String internalUserID = "w_" + RandomStringUtils.randomAlphanumeric(12).toLowerCase()

    String authToken = RandomStringUtils.randomAlphanumeric(12).toLowerCase()

    log.debug "Auth token: " + authToken

    String sessionToken = RandomStringUtils.randomAlphanumeric(16).toLowerCase()

    log.debug "Session token: " + sessionToken

    String externUserID = params.userID
    if (StringUtils.isEmpty(externUserID)) {
      externUserID = internalUserID
    }

    def roleId = null;
    String matrixToken = getMatrixTokenCookie();
    if (matrixToken != null) {
      def matrixTokenObj = new groovy.json.JsonSlurper().parseText(matrixToken)
      roleId = matrixTokenObj.RoleId
    }

    //Return a Map with the user custom data
    Map<String, String> userCustomData = meetingService.getUserCustomData(meeting, externUserID, params);

    //Currently, it's associated with the externalUserID
    if (userCustomData.size() > 0)
      meetingService.addUserCustomData(meeting.getInternalId(), externUserID, userCustomData);

    String guestStatusVal = meeting.calcGuestStatus(role, guest, authenticated)

    String empId = params.empId;

    def userCookie = getUserCookie();

    UserSession us = new UserSession();
    us.authToken = authToken;
    us.internalUserId = internalUserID
    us.conferencename = meeting.getName()
    us.meetingID = meeting.getInternalId()
    us.externMeetingID = meeting.getExternalId()
    us.externUserID = externUserID
    us.fullname = fullName
    us.empId = empId
    us.role = role
    us.roleId = roleId
    us.conference = meeting.getInternalId()
    us.room = meeting.getInternalId()
    us.voicebridge = meeting.getTelVoice()
    us.webvoiceconf = meeting.getWebVoice()
    us.mode = "LIVE"
    us.record = meeting.isRecord()
    us.welcome = meeting.getWelcomeMessage()
    us.guest = guest
    us.authed = authenticated
    us.guestStatus = guestStatusVal
    us.logoutUrl = meeting.getLogoutUrl()
    us.defaultLayout = meeting.getMeetingLayout()
    us.leftGuestLobby = false
    us.ipAddress = clientIp;
    
    if (userCookie != null ) {
      us.setUserCookie(userCookie);
    }

    if (!StringUtils.isEmpty(params.defaultLayout)) {
      us.defaultLayout = params.defaultLayout;
    }

    if (!StringUtils.isEmpty(params.avatarURL)) {
      us.avatarURL = params.avatarURL;
    } else {
      us.avatarURL = meeting.defaultAvatarURL
    }

    if (!StringUtils.isEmpty(params.excludeFromDashboard)) {
      try {
        us.excludeFromDashboard = Boolean.parseBoolean(params.excludeFromDashboard)
      } catch (Exception e) {
        // Do nothing, prop excludeFromDashboard was already initialized
      }
    }

    String meetingId = meeting.getInternalId()

    if (hasReachedMaxParticipants(meeting, us)) {
      // BEGIN - backward compatibility
      invalid("maxParticipantsReached", "The number of participants allowed for this meeting has been reached.", REDIRECT_RESPONSE);
      return
      // END - backward compatibility

      errors.maxParticipantsReached();
      respondWithErrors(errors, REDIRECT_RESPONSE);
      return;
    }

    // Register user into the meeting.
    meetingService.registerUser(
        us.meetingID,
        us.internalUserId,
        us.fullname,
        us.role,
        us.externUserID,
        us.authToken,
        us.avatarURL,
        us.guest,
        us.authed,
        guestStatusVal,
        us.excludeFromDashboard,
        us.leftGuestLobby
    )

    session.setMaxInactiveInterval(paramsProcessorUtil.getDefaultHttpSessionTimeout())

    //check if exists the param redirect
    boolean redirectClient = true;
    String clientURL = paramsProcessorUtil.getDefaultHTML5ClientUrl();

    if (!StringUtils.isEmpty(params.redirect)) {
      try {
        redirectClient = Boolean.parseBoolean(params.redirect);
      } catch (Exception e) {
        redirectClient = true;
      }
    }

    String msgKey = "successfullyJoined"
    String msgValue = "You have joined successfully."

    // Keep track of the client url in case this needs to wait for
    // approval as guest. We need to be able to send the user to the
    // client after being approved by moderator.
    us.clientUrl = clientURL + "?sessionToken=" + sessionToken

    session[sessionToken] = sessionToken
    meetingService.addUserSession(sessionToken, us)

    //Identify which of these to logs should be used. sessionToken or user-token
    // log.info("Session sessionToken for " + us.fullname + " [" + session[sessionToken] + "]")
    // log.info("Session user-token for " + us.fullname + " [" + session['user-token'] + "]")

    // log.info("Session token: ${sessionToken}")

    // Process if we send the user directly to the client or
    // have it wait for approval.
    String destUrl = clientURL + "?sessionToken=" + sessionToken
    if (guestStatusVal.equals(GuestPolicy.WAIT)) {
      String guestWaitUrl = paramsProcessorUtil.getDefaultGuestWaitURL();
      destUrl = guestWaitUrl + "?sessionToken=" + sessionToken
      // Check if the user has her/his default locale overridden by an userdata
      String customLocale = userCustomData.get("bbb_override_default_locale")
      if (customLocale != null) {
        destUrl += "&locale=" + customLocale
      }
      msgKey = "guestWait"
      msgValue = "Guest waiting for approval to join meeting."
      redirectClient = false
    } else if (guestStatusVal.equals(GuestPolicy.DENY)) {
      invalid("guestDeniedAccess", "You have been denied access to this meeting based on the meeting's guest policy", REDIRECT_RESPONSE)
      return
    } else {
      if (Config.restrictFromSameIP) {
        //* Checking the same IP Condition
        clientIp = request.getHeader('X-Forwarded-For')  // Getting Request from nginx so need to get X-Forwarded-For
        if (guest == false) {
            meeting.addAdvisorIP(clientIp)
        } else {
          meeting.addCustomerIP(clientIp)
        }
        if(isIpAddressSame(meeting, clientIp, guest)) { 
          invalid("alreadyJoined", "You have opened the PBMeet tab already, please close that tab first and try again", false)
          return;
        }
      }
    }

    Map<String, Object> logData = new HashMap<String, Object>();
    logData.put("meetingid", us.meetingID);
    logData.put("extMeetingid", us.externMeetingID);
    logData.put("name", us.fullname);
    logData.put("userid", us.internalUserId);
    logData.put("sessionToken", sessionToken);
    logData.put("logCode", "join_api");
    logData.put("description", "Handle JOIN API.");

    Gson gson = new Gson();
    String logStr = gson.toJson(logData);

    // log.info(" --analytics-- data=" + logStr);

    if (redirectClient) {
      log.info("Redirecting to ${destUrl}");
      redirect(url: destUrl);
    } else {
      log.debug "Rendering as json"
      def builder = new JsonBuilder()
      builder.response {
          returncode RESP_CODE_SUCCESS
          messageKey msgKey
          message msgValue
          meetingName meeting.getName()
          meetingID meeting.getExternalId()
          internalUserId us.internalUserId
          auth_token us.authToken
          session_token session[sessionToken]
          guestStatus guestStatusVal
          url destUrl
      }

      render(contentType: "application/json", text: builder.toPrettyString())


      // log.info("Successfully joined. Sending XML response.");
      // response.addHeader("Cache-Control", "no-cache")
      // withFormat {
      //   xml {
      //     render(text: responseBuilder.buildJoinMeeting(us, session[sessionToken], guestStatusVal, destUrl, msgKey, msgValue, RESP_CODE_SUCCESS), contentType: "text/xml")
      //   }
      // }
    }
  }

def updateMeetingRoomHandler = {
  try {
    // Constants
    String API_CALL = 'updateMeetingRoom'

    // Log API call and request details
    log.debug CONTROLLER_NAME + "#${API_CALL}"
    log.debug request.getParameterMap().toMapString()
    log.debug request.getQueryString()

    // Validate request
    Map.Entry<String, String> validationResponse = validateRequest(
      ValidationService.ApiCall.UPDATE_MEETING_ROOM,
      request.getParameterMap(),
      request.getQueryString(),
    )

    // Handle validation errors
    if (!(validationResponse == null)) {
      invalid(validationResponse.getKey(), validationResponse.getValue())
      return
    }

    // Get roomId and internalMeetingId from request parameters
    def meetingId = params.meetingId;
    Meeting m = meetingService.getMeetingFromMeetingId(meetingId);

    // Check if meeting room exists
    if (m == null) {
      def errorResponse = [
        success: false,
        status: 204,
        message: "Meeting Room Not Found"
      ]

      log.error("### $meetingId: ERROR => updateMeetingRoom => response: $errorResponse")

      render(status: 200, text: JsonOutput.toJson(errorResponse), contentType: "application/json")
      return;
    }

    // Update agent details if provided
    def agentId = params.agentId;
    def agentName = params.agentName;
    def agentPwd = params.agentId;

    if (agentId) {
      m.setAgentId(agentId);
    }

    if (agentName) {
      m.setAgentName(agentName);
    }

    // Prepare success response
    def responseBody = [
      success: true,
      status: 200,
      message: "Meeting Room data Updated successfully"
    ]

    log.info("### $meetingId => updateMeetingRoom => response: $responseBody | agentId: $agentId | agentName: $agentName");

    render(status: 200, text: JsonOutput.toJson(responseBody), contentType: "application/json")

  } catch (Exception e) {
    def errorResponse = [
      success: false,
      status: 500,
      message: "An error occurred while updating the meeting room",
      error: e.getMessage()
    ]

    log.error("### $meetingId: ERROR => updateMeetingRoom => response: $errorResponse")
    render(status: 200, text: JsonOutput.toJson(errorResponse), contentType: "application/json")
  }
}

def renderErrorResponse(String message) {
    def errorData = [success: false, message: message]
    log.error("Error Response: ${message}")
    response.addHeader("Cache-Control", "no-cache")
    render(text: JsonOutput.toJson(errorData), contentType: "application/json")
}

/*******************************************
   * GET_AGENT_URL_BY_MEETINGID API
    This API is for getting Agent URL for VC_IB Call
*******************************************/

def getAgentIntoQueue = {
  try {
    def requestBody = request.JSON

    if (!requestBody?.meetingId || !requestBody?.campaign) {
      return renderErrorResponse("meetingId and campaign are required.")
    }

    def meetingId = requestBody.meetingId
    def campaign = requestBody.campaign
    def agentId = requestBody.agentId;

    // Fetch meeting details
    Meeting meeting = meetingService.getMeetingFromMeetingId(meetingId)

    // Fetch PBMeet configuration
    def pbmeetConfig = PBMeetV2ConfigService.getInstance()?.getConfig()
    def queueArray = pbmeetConfig?.VCQueues?.get(campaign)
    def currentQueue = null

    if (!queueArray || queueArray.isEmpty()) {
        currentQueue = null
        log.info("No queue found for campaign: ${campaign}, setting currentQueue to null")
    } else {
        currentQueue = queueArray[0].getAsString()
        log.info("Selected queue: ${currentQueue} for campaign: ${campaign}")
    }

    if (campaign == "health_ped_doc_conf") {
      agentId = getAssignedAgentId(meeting.getLeadId()?.toInteger(), agentId)
    }

    // Construct API request payload
    def requestData = [
        meetingID: meetingId,
        leadId: meeting.getLeadId()?.toInteger(),
        agentId: agentId,
        campaign: currentQueue
    ]

    log.info("Sending request to makeCallV2: ${requestData}")

    // Build and send external API request
    ExternalAPI api = new ExternalAPI.ExternalAPIBuilder()
        .addMethod("POST")
        .addService("dialer")
        .addRequestBody(requestData)
        .build()

    def url = 'api/v2/pbmeet/makeCallV2'
    def res = api.CALL_API(url)
    def responseObj = new groovy.json.JsonSlurper().parseText(res)

    log.info("makeCallV2 API Response: ${res}")

    def success = responseObj?.status == 200
    def message = success ? "Call to agent done successfully!" : "Call to agent failed!"

    def responseBody = [
      success: success,
      message: message
    ]

    //  ******** API Call for updating agent find time *************
    if (success) {
      def apiUrl = "/pbmeet/v2/updateAgentFindStartTimeVCIB"

      def apiReqData = [
        meetingId: meetingId
      ]

      log.info("RequestData for /pbmeet/v2/updateAgentFindStartTimeVCIB API is: " + apiReqData)

      ExternalAPI apiReq = new ExternalAPI.ExternalAPIBuilder()
        .addMethod("POST")
        .addService("pbmeetApi")
        .addRequestBody(apiReqData)
        .build(); 

      def apiRes = apiReq.CALL_API(apiUrl)
      log.info("updateAgentFindStartTimeVCIB API Response: " + apiRes);
    }

    // ********************************************************* //

    JsonDemo resObj = new JsonDemo(responseBody);

    response.addHeader("Cache-Control", "no-cache")
    withFormat {
        json {
            log.debug "Rendering as json"
            render(text: JsonOutput.toJson(resObj), contentType: "application/json")
        }
    }

  } catch (Exception e) {
      log.error("Error in getAgentIntoQueue: ${e.message}", e)
      return renderErrorResponse("An unexpected error occurred!")
  }
}

  def pbmeetV2Logs = {
    String API_CALL = 'pbmeetV2Logs'
    log.debug "${API_CALL} - Store Logs in PbmeetV2"

    try {
        String requestBody = request.inputStream?.text
        def requestJson = new groovy.json.JsonSlurper().parseText(requestBody ?: '{}')

        String url = "/pbmeet/v2/log/pbmeetV2Logs"

        def requestBodyMap = [
          methodName: requestJson.methodName,
          trackingId: requestJson.trackingId,
          requestText: requestJson.requestText,
          responseText: requestJson.responseText,
          exception: requestJson.exception
        ]

        ExternalAPI api = new ExternalAPI.ExternalAPIBuilder()
            .addMethod("POST")
            .addService("pbmeetApi")
            .addRequestBody(requestBodyMap)
            .build()

        def apiResponse
        try {
            apiResponse = api.CALL_API(url)
            log.info("${API_CALL} - pbmeetV2Logs API Response: ${apiResponse}")
        } catch (Exception e) {
            log.error("${API_CALL} - Error calling pbmeetV2Logs API", e)
            apiResponse = '{"message": "Error calling API"}'
        }


        def responseObj = new groovy.json.JsonSlurper().parseText(apiResponse)

        response.addHeader("Cache-Control", "no-cache")
        withFormat {
          json {
            log.debug "Rendering as json"
            render(text: JsonOutput.toJson(responseObj), contentType: "application/json")
          }
        }
    } catch (Exception e) {
      log.error("${API_CALL} - Unexpected error", e)
    }
  }	

/*******************************************
   * INSERT_MIC_PERMISSION_LOGS API
    This API is for inserting mic permission logs in table
*******************************************/

def insertMicPermissionLogs = {
  String API_CALL = 'insertMicPermissionLogs'
  log.debug CONTROLLER_NAME + "#${API_CALL}"
  def requestBody = request.JSON;
  
  def internalMeetingId = requestBody.internalMeetingId;
  def isAgent = requestBody.isAgent;
  def permission = requestBody.permission;

  def requestData = [
    internalMeetingId: internalMeetingId,
    isAgent: isAgent,
    permission: permission
  ]

  def url = "/pbmeet/v2/micPermissionLogs"

  log.info("RequestData for /pbmeet/v2/micPermissionLogs API is: " + requestData)

  ExternalAPI api = new ExternalAPI.ExternalAPIBuilder()
    .addMethod("POST")
    .addService("pbmeetApi")
    .addRequestBody(requestData)
    .build();

  def response = api.CALL_API(url)

  log.info("micPermissionLogs API Response: " + response);
  render status: 200, text: response
}

/*******************************************
   * UPDATE_IB_CUSTOMER_CANCLE_TIME API
    This API is for getting Agent URL for VC_IB Call
*******************************************/
def updateIBCustomerCancelTime = {
  def meetingId = params.meetingId;
  def reason = params.reason;

  def url = "/pbmeet/v2/updateIBCustomerCancelTime"

  def requestData = [
    meetingId: meetingId,
    leaveReason: reason
  ]

  ExternalAPI api = new ExternalAPI.ExternalAPIBuilder()
    .addMethod("POST")
    .addService("pbmeetApi")
    .addRequestBody(requestData)
    .build(); 

  def response = api.CALL_API(url)
  
  log.info("updateIBCustomerCancelTime API Response: " + response);
  render status: 200, text: response

}

/*******************************************
   * Assign this lead API
    This API is for assign the vc_ib lead to agent
*******************************************/
def assignVCIBLead = {
  String API_CALL = 'assignVCIBLead'
  log.debug CONTROLLER_NAME + "#${API_CALL}"
  
  try {
    def requestBody = request.JSON

    def meetingId = requestBody.meetingId
    def agentId = requestBody.assignedToAgent
    def productId = requestBody.productId
    def jobId = requestBody.jobId


    Meeting m = meetingService.getMeetingFromMeetingId(meetingId);
    if (m == null) {
      def errorResponse = [
        success: false,
        status: 404,
        message: "Meeting not found with ID: ${meetingId}"
      ]
      log.error("${API_CALL} - Meeting not found: ${meetingId}")
      render(status: 404, text: JsonOutput.toJson(errorResponse), contentType: "application/json")
      return
    }

    def leadId = m.getLeadId();

    log.info("${API_CALL} - Processing assignment: meetingId=${meetingId}, agentId=${agentId}, leadId=${leadId}, productId=${productId}, jobId=${jobId}")

    def url = "/pbmeet/matrix/assignVCIBLead"

    def requestData = [
      assignedToAgent: agentId,
      assignedBy: 124,
      productId: productId,
      leadId: leadId,
      groupId: 0,
      flag: 1,
      jobId: jobId
    ]

    // Call external API
    ExternalAPI api = new ExternalAPI.ExternalAPIBuilder()
      .addMethod("POST")
      .addService("pbmeetApi")
      .addRequestBody(requestData)
      .build();

    def externalResponse = api.CALL_API(url)
    
    log.info("${API_CALL} - External API Response: ${externalResponse}")
    
    def responseObj = new groovy.json.JsonSlurper().parseText(externalResponse)

    // Check if external API call was successful
    if (responseObj && responseObj.success !== false) {
      def successResponse = [
        success: true,
        status: 200,
        message: "Lead assigned successfully",
        data: [
          meetingId: meetingId,
          agentId: agentId,
          leadId: leadId,
          productId: productId,
          jobId: jobId
        ],
        externalResponse: responseObj
      ]
      
      log.info("${API_CALL} - Success: Lead ${leadId} assigned to agent ${agentId}")
      render(status: 200, text: JsonOutput.toJson(successResponse), contentType: "application/json")
    } else {
      def errorResponse = [
        success: false,
        status: 500,
        message: "External API call failed",
        externalResponse: responseObj
      ]
      
      log.error("${API_CALL} - External API failed: ${responseObj}")
      render(status: 500, text: JsonOutput.toJson(errorResponse), contentType: "application/json")
    }

  } catch (Exception e) {
    log.error("${API_CALL} - Unexpected error: ${e.message}", e)
    def errorResponse = [
      success: false,
      status: 500,
      message: "An unexpected error occurred while assigning the lead",
      error: e.getMessage()
    ]
    render(status: 500, text: JsonOutput.toJson(errorResponse), contentType: "application/json")
  }
}

/*******************************************
   * CLEAR_CAHCE API
    This API is for clearing PbmeetV2 Config.
*******************************************/

def clearCache() {

    String API_CALL = 'clearCache'
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    Map.Entry<String, String> validationResponse = validateRequest(
            ValidationService.ApiCall.CLEAR_CACHE,
            request.getParameterMap(),
            request.getQueryString()
    )

    if(!(validationResponse == null)) {
      invalid(validationResponse.getKey(), validationResponse.getValue())
      return
    }

    PBMeetV2ConfigService pbmeetConfig = PBMeetV2ConfigService.getInstance()
    pbmeetConfig.makeConfigStale()

    render(status: 200, text: "Cache cleared successfully")
}

/*******************************************
   * GET_AGENT_URL_BY_MEETINGID API
    For VC IB Task
*******************************************/
  def getAgentURLByMeetingIdHandler = {
    try {
      String API_CALL = 'getAgentURLByMeetingId'
      log.debug CONTROLLER_NAME + "#${API_CALL}"
      log.debug request.getParameterMap().toMapString()
      log.debug request.getQueryString()

      Map.Entry<String, String> validationResponse = validateRequest(
              ValidationService.ApiCall.GET_AGENT_URL_BY_MEETINGID,
              request.getParameterMap(),
              request.getQueryString()
      )

      if(!(validationResponse == null)) {
          invalid(validationResponse.getKey(), validationResponse.getValue())
          return
      }

      def meetingId = params.meetingId;
      def agentName = params.agentName;
      def agentId = params.agentId;

      Meeting meeting = meetingService.getMeetingFromMeetingId(meetingId);

      if (meeting == null) {
        def errorData = [
            success: false,
            message: "Room Not Found!!",
          ]
          JsonDemo errResponse = new JsonDemo(errorData);
          response.addHeader("Cache-Control", "no-cache")
          withFormat {
            json {
              log.debug "Rendering as json"
              render(text: JsonOutput.toJson(errResponse), contentType: "application/json")
            }
          }
          return;
      }

      String method = "join"
      String extId = validationService.encodeString(meeting.getExternalId())
      
      String hashString = meetingId + "_agent" + validationService.getSecuritySalt()
      String expectedHash = DigestUtils.sha256Hex(hashString);

      String fullName = validationService.encodeString(agentName);
      String password = meeting.getModeratorPassword();
      
      String query = "fullName=${fullName}&meetingID=${extId}&password=${password}&empId=${agentId}&redirect=true"
      String checksum = DigestUtils.sha256Hex(method + query + validationService.getSecuritySalt())
      
      String defaultServerUrl = paramsProcessorUtil.defaultServerUrl
      String agentUrl = "${defaultServerUrl}/bigbluebutton/api/${method}?${query}&checksum=${checksum}"

      def responseBody = [
        success: true,
        message: "Advisor Link Generated Successfully!!!",
        data : [
          agentLink: agentUrl,
          agentSid: expectedHash
        ]
      ]

      JsonDemo res = new JsonDemo(responseBody);

      response.addHeader("Cache-Control", "no-cache")
      withFormat {
          json {
              log.debug "Rendering as json"
              render(text: JsonOutput.toJson(res), contentType: "application/json")
          }
      }
    } catch (Exception e) {
      log.error "An error occurred: ${e.message}"
      def errorResponse = [
              success: false,
              message: "Failed to generate Advisor Link. Please try again later."
      ]
      JsonDemo res = new JsonDemo(errorResponse);
      render(text: JsonOutput.toJson(res), contentType: "application/json")
    }
}


// This function is for getting Join Link in Voip Call
def getJoinLinkWithChecksum(String meetingId, String name, String agentId, String password, boolean isAgent) {
  String method = "join"

  String fullName = validationService.encodeString(name);
  String query;

  if (isAgent) {
    query = "fullName=${fullName}&meetingID=${meetingId}&password=${password}&empId=${agentId}&redirect=true"
  } else {
    query = "fullName=${fullName}&meetingID=${meetingId}&password=${password}&role=VIEWER&guest=true&redirect=true"
  }

  String checksum = DigestUtils.sha256Hex(method + query + validationService.getSecuritySalt())
  
  String defaultServerUrl = paramsProcessorUtil.defaultServerUrl
  String url = "${defaultServerUrl}/bigbluebutton/api/${method}?${query}&checksum=${checksum}"
  return url;
}

/*******************************************
   * GET_INTERNAL_JOIN_LINK API
    This is for VOIP
*******************************************/
  def getInternalJoinLinkHandler = {
    try {
      String API_CALL = 'getInternalJoinLink';
      log.debug CONTROLLER_NAME + "#${API_CALL}"
      log.debug request.getParameterMap().toMapString()
      log.debug request.getQueryString()

      Map.Entry<String, String> validationResponse = validateRequest(
              ValidationService.ApiCall.GET_INTERNAL_JOIN_LINK,
              request.getParameterMap(),
              request.getQueryString()
      )

      if(!(validationResponse == null)) {
          invalid(validationResponse.getKey(), validationResponse.getValue())
          return
      }

      def meetingId = params.meetingId;
      def agentName = params.agentName;
      def customerName = params.customerName;

      Meeting meeting = meetingService.getMeetingFromMeetingId(meetingId);

      if (meeting == null) {
        def errorData = [
            success: false,
            message: "Room Not Found!!",
            data: null,
          ]
          response.addHeader("Cache-Control", "no-cache")
          withFormat {
            json {
              log.debug "Rendering as json"
              render(text: JsonOutput.toJson(errorData), contentType: "application/json")
            }
          }
          return;
      }

      def agentId = meeting.getAgentId();
      def agentLink = getJoinLinkWithChecksum(meetingId, agentName, agentId, meeting.getModeratorPassword(), true)
      def customerLink = getJoinLinkWithChecksum(meetingId, customerName, null, meeting.getViewerPassword(), false)

      def responseBody = [
        success: true,
        message: "Both Links Generated Successfully!!!",
        data : [
          "agentLink": agentLink,
          "customerLink": customerLink
        ]
      ]

      response.addHeader("Cache-Control", "no-cache")
      withFormat {
          json {
              log.debug "Rendering as json"
              render(text: JsonOutput.toJson(responseBody), contentType: "application/json")
          }
      }
    } catch (Exception e) {
      log.error "An error occurred: ${e.message}"
      def errorResponse = [
              success: false,
              message: "Failed to generate Join Links. Please try again later.",
              data: null,
      ]
      render(text: JsonOutput.toJson(errorResponse), contentType: "application/json")
    }
}


  /*******************************************
   * IS_MEETING_RUNNING API
   *******************************************/
  def isMeetingRunning = {
    String API_CALL = 'isMeetingRunning'
    log.debug CONTROLLER_NAME + "#${API_CALL}"
    log.debug request.getParameterMap().toMapString()
    log.debug request.getQueryString()

    Map.Entry<String, String> validationResponse = validateRequest(
            ValidationService.ApiCall.MEETING_RUNNING,
            request.getParameterMap(),
            request.getQueryString()
    )

    if(!(validationResponse == null)) {
      invalid(validationResponse.getKey(), validationResponse.getValue())
      return
    }

    Meeting meeting = ServiceUtils.findMeetingFromMeetingID(params.meetingID);
    boolean isRunning = meeting != null && meeting.isRunning();

    response.addHeader("Cache-Control", "no-cache")
    withFormat {
      xml {
        render(contentType: "text/xml") {
          render(text: responseBuilder.buildIsMeetingRunning(isRunning, RESP_CODE_SUCCESS), contentType: "text/xml")
        }
      }
    }
  }

  /************************************
   * END API
   ************************************/
  def end = {
    String API_CALL = "end"
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    Map.Entry<String, String> validationResponse = validateRequest(
            ValidationService.ApiCall.END,
            request.getParameterMap(),
            request.getQueryString()
    )

    if(!(validationResponse == null)) {
      invalid(validationResponse.getKey(), validationResponse.getValue())
      return
    }

    Meeting meeting = ServiceUtils.findMeetingFromMeetingID(params.meetingID);

    Map<String, Object> logData = new HashMap<String, Object>();
    logData.put("meetingid", meeting.getInternalId());
    logData.put("extMeetingid", meeting.getExternalId());
    logData.put("name", meeting.getName());
    logData.put("logCode", "end_api");
    logData.put("description", "Handle END API.");

    Gson gson = new Gson();
    String logStr = gson.toJson(logData);

    // log.info(" --analytics-- data=" + logStr);

    meetingService.endMeeting(meeting.getInternalId());

    response.addHeader("Cache-Control", "no-cache")
    withFormat {
      xml {
        render(contentType: "text/xml") {
          render(text: responseBuilder.buildEndRunning("sentEndMeetingRequest", "A request to end the meeting was sent.  Please wait a few seconds, and then use the getMeetingInfo or isMeetingRunning API calls to verify that it was ended.", RESP_CODE_SUCCESS), contentType: "text/xml")
        }
      }
    }
  }

  def getMatrixTokenCookie() {
    String matrixToken = CookieManager.getItem("MatrixToken", request);
    if (matrixToken == null) {
      return null;
    }
     def decodedBytes = java.util.Base64.getDecoder().decode(matrixToken)
     def jsonString = new String(decodedBytes, "UTF-8");
     def json = new groovy.json.JsonSlurper().parseText(jsonString);
     return JsonOutput.toJson(json);
  }

  // Function for getting User Cookie
  def getUserCookie() {
    String userCookie = CookieManager.getItem("User", request);
    if (userCookie == null) {
      return null;
    }
    def decodedBytes = java.util.Base64.getDecoder().decode(userCookie)  // decoding user cookie value as it is in base 64
    def jsonString = new String(decodedBytes, "UTF-8")
    def json = new groovy.json.JsonSlurper().parseText(jsonString)
    return JsonOutput.toJson(json)
  }

  /*****************************************
   * GETMEETINGINFO API
   *****************************************/
  def getMeetingInfo = {
    String API_CALL = "getMeetingInfo"
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    Map.Entry<String, String> validationResponse = validateRequest(
            ValidationService.ApiCall.GET_MEETING_INFO,
            request.getParameterMap(),
            request.getQueryString()
    )

    // if(!(validationResponse == null)) {
    //   invalid(validationResponse.getKey(), validationResponse.getValue())
    //   return
    // }

    String matrixAuthCookie = getMatrixTokenCookie();

    try {

      Meeting meeting = ServiceUtils.findMeetingFromMeetingID(params.meetingID);

      if(meeting == null) {
        
        def meetingId = null
        def queryString = request.getQueryString();

        if (queryString) {
          def params = queryString.split('&')

          params.each { param ->
            def keyValue = param.split('=')
            if (keyValue.size() == 2 && keyValue[0] == 'meetingID') {
                meetingId = keyValue[1]
                return
            }
          } 
        }

        def meetingData = MeetingDataCache.getRoomDataFromCache(meetingId)
        
        def flag = false;

        if(meetingData != null) {
          
          def meetingDataCopy = meetingData.clone();
          meetingDataCopy.remove("createdOn");
          meetingDataCopy.put("roomId", meetingId);

          if (meetingData.get("process").equals("vc_ib")) {
            meetingDataCopy.put("guestPolicy", "ALWAYS_ACCEPT");
          }
          
          def url = "/pbmeet/v2/create"

          ExternalAPI api = new ExternalAPI.ExternalAPIBuilder()
            .addMethod("POST")
            .addService("pbmeetApi")
            .addRequestBody(meetingDataCopy)
            .build(); 

          def response = api.CALL_API(url)
          def jsonMap = new groovy.json.JsonSlurper().parseText(response)

          flag = jsonMap?.data?.agentLink?.trim() != null
        }

        if(flag == false){
          def errorData = [
            returncode: RESP_CODE_FAILED,
            message: "This presentation link has been expired.",
            errorType: "ROOM_EXPIRE_ERROR",
            isAgent: matrixAuthCookie != null
          ]
          JsonDemo errResponse = new JsonDemo(errorData);
          response.addHeader("Cache-Control", "no-cache")
          withFormat {
            json {
              log.debug "Rendering as json"
              render(text: JsonOutput.toJson(errResponse), contentType: "application/json")
            }
          }
          log.info("### $meetingId: ERROR => : ${JsonOutput.toJson(errResponse)}")
          return;
        }      
      }
      meeting = ServiceUtils.findMeetingFromMeetingID(params.meetingID);
    
      if (matrixAuthCookie != null) {
        // checking if this is agent or not
        // We will get user cookie in case of advisor only
        def userCookie = getUserCookie();
        meeting.setDataFromUserCookie(userCookie);
      }
      
      String meetingId = params.meetingID;
      Long createTimeMillis = meeting.getCreateTime()
      Date utcDate = new Date(createTimeMillis)
      SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss")
      formatter.setTimeZone(TimeZone.getTimeZone("Asia/Kolkata"))
      String createdOn = formatter.format(utcDate)

      String method = "join"
      String extId = validationService.encodeString(meeting.getExternalId())
      String password
      String role = "VIEWER"
      String query
      String fullName;
      String name;

      // Checking hash is same or not
      String hash = params.sid;
      String hashString = ""
      if(matrixAuthCookie == null) {
        hashString = meetingId + "_customer" + validationService.getSecuritySalt()
      } else {
        hashString = meetingId + "_agent" + validationService.getSecuritySalt()
      }
      String expectedHash = DigestUtils.sha256Hex(hashString) 
      log.info("##############################  expected hash: " + expectedHash);
      log.info("##############################  current hash: " + hash);
      if(hash != expectedHash){

        def errorData = [
          returncode: RESP_CODE_FAILED,
          message: "Invalid meeting link",
          errorType: "INVALID_MEETING_LINK",
          isAgent: false, 
        ]
        JsonDemo errResponse = new JsonDemo(errorData)
        response.addHeader("Cache-Control", "no-cache")
        withFormat {
          json {
            log.debug "Rendering as json"
            render(text: JsonOutput.toJson(errResponse), contentType: "application/json")
          }
        }
        log.error("### $meetingId: ERROR => ${meeting.getLeadId()}: ${JsonOutput.toJson(errResponse)}")
        return;
      }
      if(matrixAuthCookie == null) {
        // Customer
        name = meeting.getCustomerName();
        fullName = validationService.encodeString(meeting.getCustomerName())
        password = meeting.getViewerPassword();
        query = "fullName=${fullName}&meetingID=${extId}&password=${password}&role=${role}&redirect=false&guest=true"
      } else {
        // Agent
        def process = meeting.getProcess();
        def matrixTokenObj = new groovy.json.JsonSlurper().parseText(matrixAuthCookie);
        def agentId = matrixTokenObj.EmployeeId
        if (process.equals("vc_ib")) {
          def userCookie = getUserCookie();
          def json = new groovy.json.JsonSlurper().parseText(userCookie);
          name = json.UserName;
          fullName = validationService.encodeString(json.UserName)
        } else {
          name = meeting.getAgentName()
          fullName = validationService.encodeString(meeting.getAgentName());
        }
        password = meeting.getModeratorPassword();
        query = "fullName=${fullName}&meetingID=${extId}&password=${password}&empId=${agentId}&redirect=false"
      }

      String checksum = DigestUtils.sha256Hex(method + query + validationService.getSecuritySalt())
      String defaultServerUrl = paramsProcessorUtil.defaultServerUrl
      String url = "${defaultServerUrl}/bigbluebutton/api/${method}?${query}&checksum=${checksum}"

      def responseBody = [
        returncode: RESP_CODE_SUCCESS,
        meetingName: meeting.getName(),
        name: name,
        meetingID: meeting.getExternalId(),
        internalMeetingID: meeting.getInternalId(),
        // parentMeetingID: meeting.getParentMeetingId(),
        agentId: meeting.getAgentId(),
        productID: meeting.getProductId(),
        createTime: meeting.getCreateTime(),
        createDate: createdOn,
        // voiceBridge: true,
        voiceBridge: meeting.getTelVoice(),
        dialNumber: meeting.getDialNumber(),
        moderatorPW: meeting.getModeratorPassword(),
        attendeePW: meeting.getViewerPassword(),
        duration: meeting.getDuration(),
        hasUserJoined: meeting.hasUserJoined(),
        // hasBeenForciblyEnded: "desdfdsf",
        hasBeenForciblyEnded: meeting.isForciblyEnded(),
        joinURL: url,
        isAgent: matrixAuthCookie != null,
        process: meeting.getProcess(),
        campaign: meeting.getCampaign()
        // messageKey: msgKey,
        // message: msg,
      ]

      JsonDemo res = new JsonDemo(responseBody);

      response.addHeader("Cache-Control", "no-cache")
      withFormat {
        json {
          log.debug "Rendering as json"
          render(text: JsonOutput.toJson(res), contentType: "application/json")
        }
      }
      log.info("### $meetingId => getMeetingInfo => response: ${responseBody}")

    } catch (Exception e) {

        def errorData = [
            returncode: RESP_CODE_FAILED,
            message: "Something went wrong",
            errorType: "SOMETHING_WENT_WRONG",
            isAgent: matrixAuthCookie != null, 
            error: e.toString(),
        ]
        JsonDemo errResponse = new JsonDemo(errorData)
        response.addHeader("Cache-Control", "no-cache")
        withFormat {
          json {
            log.debug "Rendering as json"
            render(text: JsonOutput.toJson(errResponse), contentType: "application/json")
          }
        }
        log.error("### ERROR => ${JsonOutput.toJson(errResponse)}")
        return;
    }
  }

    def formatPrettyDate(Long timestamp) {
      return new Date(timestamp).toString();
    }

  /************************************
   *  GETMEETINGS API
   ************************************/
  def getMeetingsHandler = {
    String API_CALL = "getMeetings"
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    Map.Entry<String, String> validationResponse = validateRequest(
            ValidationService.ApiCall.GET_MEETINGS,
            request.getParameterMap(),
            request.getQueryString()
    )

    if(!(validationResponse == null)) {
      invalid(validationResponse.getKey(), validationResponse.getValue())
      return
    }

    Collection<Meeting> mtgs = meetingService.getMeetings();

    if (mtgs == null || mtgs.isEmpty()) {
      response.addHeader("Cache-Control", "no-cache")
      withFormat {
        xml {
          render(text: responseBuilder.buildGetMeetingsResponse(mtgs, "noMeetings", "no meetings were found on this server", RESP_CODE_SUCCESS), contentType: "text/xml")
        }
      }
    } else {
      response.addHeader("Cache-Control", "no-cache")

      withFormat {
        xml {
          render(text: responseBuilder.buildGetMeetingsResponse(mtgs, null, null, RESP_CODE_SUCCESS), contentType: "text/xml")
        }
      }
    }
  }

  /************************************
   *  GETALLMEETINGSDATA  API
   ************************************/
  def getAllMeetingsData = {
    String API_CALL = "getAllMeetingsData"
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    Map.Entry<String, String> validationResponse = validateRequest(
            ValidationService.ApiCall.GET_ALL_MEETINGS_DATA,
            request.getParameterMap(),
            request.getQueryString()
    )

    if(!(validationResponse == null)) {
      invalid(validationResponse.getKey(), validationResponse.getValue())
      return
    }

    Collection<Meeting> mtgs = meetingService.getMeetings();
    def meetingsArr = []

    if (mtgs == null || mtgs.isEmpty()) {

      response.addHeader("Cache-Control", "no-cache")
      withFormat {
        json {
          def builder = new JsonBuilder()
          builder.response {
            returncode RESP_CODE_SUCCESS
            meetings meetingsArr
            messageKey "noMeetings"
            message "no meetings were found on this server"
          }
          render(contentType: "application/json", text: builder.toPrettyString())
        }
      }
    } else {

      mtgs.each { Meeting meeting ->
        meetingsArr << [
          meetingName: meeting.getName(),
          meetingID: meeting.getExternalId(),
          internalMeetingID: meeting.getInternalId(),
          agentName: meeting.getAgentName(),
          customerName: meeting.getCustomerName(),
          createTime: meeting.getCreateTime(),
          createDate: formatPrettyDate(meeting.getCreateTime()),
          viewerPassword: meeting.getViewerPassword(),
          agentPassword: meeting.getModeratorPassword(),
          source: meeting.getSource(),
          process: meeting.getProcess(),
          isRunning: meeting.isRunning(),
          hasUserJoined: meeting.hasUserJoined(),
          startTime: meeting.getStartTime(),
          endTime: meeting.getEndTime(),
          moderatorCount: meeting.getNumModerators(),
        ]
      }

      response.addHeader("Cache-Control", "no-cache")

      withFormat {
        json {
          def builder = new JsonBuilder()
          builder.response {
            returncode RESP_CODE_SUCCESS
            meetings meetingsArr
          }
          render(contentType: "application/json", text: builder.toPrettyString())
        }
      }
    }
  }

  // This method is for getting Join Link (specially for VC Conference)
  def getJoinLink(Meeting meeting, String name, String empId, boolean forCustomer) {
    String method = "join"
    String extId = validationService.encodeString(meeting.getExternalId())

    String resolvedName = name ?: (forCustomer ? meeting.getCustomerName() : meeting.getAgentName())
    String fullName = validationService.encodeString(resolvedName) 
    String password = forCustomer ? meeting.getViewerPassword() : meeting.getModeratorPassword();

    String employeeId = null;
    if (!forCustomer) {
      employeeId = empId ?: meeting.getAgentId(); 
    }

    String query = "fullName=${fullName}&meetingID=${extId}&password=${password}&empId=${employeeId}&redirect=true"
    String checksum = DigestUtils.sha256Hex(method + query + validationService.getSecuritySalt())
    
    String defaultServerUrl = paramsProcessorUtil.defaultServerUrl
    String url = "${defaultServerUrl}/bigbluebutton/api/${method}?${query}&checksum=${checksum}"
    
    return url
  }

  /************************************
   *  ISCUSTOMERONCALL API
   ************************************/
  def isCustomerOnCall = {
    String API_CALL = "isCustomerOnCall"
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    try {
      def leadId = params.leadId;
      Meeting m = meetingService.getMeetingFromLeadId(leadId);

      if (m == null) {
        def errorResponse = [
          isCustomerOnCall: false,
          message: "Customer is not currently on a call",
          callDetails: null,
        ]
        render(status: 200, text: JsonOutput.toJson(errorResponse), contentType: "application/json")
        return
      }

      def moderatorCount = 0
      def customerCount = 0

      m.getUsers().each { user ->
        if ("VIEWER".equals(user.getRole())) {
          customerCount += 1
        } else if ("MODERATOR".equals(user.getRole())) {
          moderatorCount += 1
        }
      }

      def isMeetingRunning = moderatorCount > 0 && customerCount > 0

      String name = params.name == "undefined" ? null : params.name
      String empId = params.empId == "undefined" ? null : params.empId

      boolean forCustomer = params.forCustomer?.toLowerCase() == "true"

      String url = getJoinLink(m, name, empId, forCustomer);
      
      def responseBody = [
          isCustomerOnCall: isMeetingRunning,
          callDetails: isMeetingRunning ? [
              agentId: m.getAgentId(),
              meetingId: m.getExternalId(),
              joinUrl: url,
          ] : null
      ]

      response.addHeader("Cache-Control", "no-cache")
      withFormat {
        json {
          log.debug "Rendering as json"
          render(text: JsonOutput.toJson(responseBody), contentType: "application/json")
        }
      }

    } catch(Exception e) {
      def errorResponse = [
          message: "Something went wrong!!",
          error: e.toString(),
        ]
        render(status: 200, text: JsonOutput.toJson(errorResponse), contentType: "application/json")
        return;
    }
  }

  /************************************
  *  ISAGENTBUSY API
  ************************************/
  def isAgentBusy = {
    String API_CALL = "isAgentBusy"
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    try {
      def agentId = params.agentId;
      Meeting m = meetingService.getMeetingFromAgentId(agentId);

      if (m == null) {
        def errorResponse = [
          IsAgentBusy: false,
          message: "Agent is currently not on any videocall"
        ]
        
        JsonDemo res = new JsonDemo(errorResponse);

        render(status: 200, text: JsonOutput.toJson(res), contentType: "application/json")
        return;
      }

      def moderatorCount = m.getNumModerators()
      def participantCount = m.getNumUsersOnline()
      def customerCount = participantCount - moderatorCount

      def isMeetingRunning = moderatorCount > 0 && customerCount > 0

      def responseBody = [
          IsAgentBusy: isMeetingRunning,
          callDetails: isMeetingRunning ? [
              agentId: m.getAgentId(),
              meetingId: m.getExternalId()
          ] : null
      ]

      JsonDemo res = new JsonDemo(responseBody);

      response.addHeader("Cache-Control", "no-cache")
      withFormat {
        json {
          log.debug "Rendering as json"
          render(text: JsonOutput.toJson(res), contentType: "application/json")
        }
      }

    } catch(Exception e) {
      def errorResponse = [
          message: "Something went wrong!!",
          error: e.toString(),
        ]
        render(status: 200, text: JsonOutput.toJson(errorResponse), contentType: "application/json")
        return;
    }
  }

   /************************************
  *  GET_FEEDBACK_QUESTIONS API
  ************************************/
  def getFeedbackQuestions = {
    String API_CALL = "getFeedbackQuestions"
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    String url = '/pbmeet/v2/feedbackQuestions'

    ExternalAPI api = new ExternalAPI.ExternalAPIBuilder()
        .addMethod("GET")
        .addService("pbmeetApi")
        .build()

    def response = api.CALL_API(url);

    render status: 200, text: response
  }

  /************************************
  *  INSERT_FEEDBACK_RESPONSES API
  ************************************/
  def insertFeedbackResponse = {
    String API_CALL = "insertFeedbackResponse"
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    String body = request.inputStream == null ? null : request.inputStream.text;

    def jsonSlurper = new JsonSlurper()
    def parsedJson = jsonSlurper.parseText(body)

    String url = '/pbmeet/v2/feedbackResponses'

    def requestBody = [
      RoomId: parsedJson.roomId,
      OptionIdArray: parsedJson.optionIdArray,
      CallType: parsedJson.callType,
      IsAgent: parsedJson.isAgent,
    ]

    ExternalAPI api = new ExternalAPI.ExternalAPIBuilder()
        .addMethod("POST")
        .addService("pbmeetApi")
        .addRequestBody(requestBody)
        .build()

    def response = api.CALL_API(url);

    log.info("insert Feedback responses: " + response);

    render status: 200, text: response
  }

  /************************************
  *  INSERT_LOADING_EVENTS API
  ************************************/
  def insertLoadingEvents = {
    String API_CALL = "insertLoadingEvents"
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    try {
      String body = request.inputStream == null ? null : request.inputStream.text;

      if (!body) {
        log.error("${API_CALL} - Request body is empty")
        render status: 200, text: "Bad Request: Missing or empty request body"
        return
      }

      def jsonSlurper = new JsonSlurper()
      def parsedJson = jsonSlurper.parseText(body)

      String url = '/pbmeet/v2/insertLoadingEventsDuration'

      if (!parsedJson?.startedAt) {
        log.error("${API_CALL} - Missing startedAt in request body")
        render status: 200, text: "Bad Request: Missing required fields (startedAt)"
        return
      }

      def requestBody = [
        initialLoadDuration: parsedJson.initialLoadDuration,
        audioModalDuration: parsedJson.audioModalDuration,
        startedAt: parsedJson.startedAt,
      ]

      ExternalAPI api = new ExternalAPI.ExternalAPIBuilder()
          .addMethod("POST")
          .addService("pbmeetApi")
          .addRequestBody(requestBody)
          .build()

      def response
      try {
          response = api.CALL_API(url)
      } catch (Exception e) {
          log.error("${API_CALL} - Error calling external API: ${e.message}", e)
          render status: 200, text: "Internal Server Error: Failed to call external API"
          return
      }

      log.info("${API_CALL} - Insert Loading Events: ${response}")

      render status: 200, text: response
    }
    catch (e) {
      log.error("${API_CALL} - Error occurred: ${e.message}", e)
        render status: 200, text: "Internal Server Error: Unexpected error occurred"
    }
  }


  /************************************
   *  CLEANUPROOM API
   ************************************/
  def cleanUpRoomHandler = {
    String API_CALL = "cleanUpRoomHandler"
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    try {
      // Validate the request
      Map.Entry<String, String> validationResponse = validateRequest(
            ValidationService.ApiCall.CLEAN_UP_ROOM,
            request.getParameterMap(),
            request.getQueryString()
      )

      // Handle validation errors
      if(!(validationResponse == null)) {
        invalid(validationResponse.getKey(), validationResponse.getValue())
        return
      }

      // Get Meetings
      Collection<Meeting> mtgs = meetingService.getMeetings();
      int endMeetingCount = 0;
          
      // Check if there are no meetings
      if (mtgs == null || mtgs.isEmpty()) {
        response.addHeader("Cache-Control", "no-cache")
        withFormat {
          json {
            def builder = new JsonBuilder()
            builder.response {
              returncode RESP_CODE_SUCCESS
              messageKey "noMeetings"
              message "no meetings were found on this server"
              endedMeetingsCount 0 
            }
            render(contentType: "application/json", text: builder.toPrettyString())
          }
        }
      } else {
        // Iterate through meetings
        mtgs.each { Meeting meeting ->

          def internalMeetingId = meeting.getInternalId();
          def startTime = meeting.getStartTime();
          // def moderatorCount = meeting.getNumModerators();
          // def participantCount = meeting.getNumUsersOnline()

          def currentTime = Instant.now().toEpochMilli()
          
          def timeDifference = currentTime - startTime;

          if(timeDifference > 5 * 60 * 60 * 1000){
            meetingService.endMeeting(internalMeetingId);
            endMeetingCount += 1;
          }
        }

        // Prepare response for ended meetings count
        response.addHeader("Cache-Control", "no-cache")
        withFormat {
          json {
            def builder = new JsonBuilder()
            builder.response {
              returncode RESP_CODE_SUCCESS
              endedMeetingsCount endMeetingCount 
            }
            render(contentType: "application/json", text: builder.toPrettyString())
          }
        }
      }
    } catch (Exception e) {
        // Handle exceptions
        log.error("${CONTROLLER_NAME}#${API_CALL} - An error occurred: ${e.message}", e)
        // Respond with an error message
        response.addHeader("Cache-Control", "no-cache")
        withFormat {
            json {
                def builder = new JsonBuilder()
                builder.response {
                    returncode RESP_CODE_FAILED
                    message "An error occurred while processing the request."
                }
                render(contentType: "application/json", text: builder.toPrettyString())
            }
        }
    }
  }

  /************************************
   *  CLEANUP ROOM WITH EMPID API
   ************************************/
  def endRoomWithEmpIdHandler = {
    String API_CALL = "endRoomWithEmpHandler"
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    try {

      def empId = params.empId;
      def password = params.salt;

      def saltArr = ['NzN6UnmQ', 'zbTkIp3E', 'I2o9MfuY']

      // Get Meetings
      Collection<Meeting> mtgs = meetingService.getMeetings();
      int endMeetingCount = 0;
     
      // Iterate through meetings
      mtgs.each { Meeting meeting ->

        def moderatorPassword = meeting.getModeratorPassword();

        if (empId == moderatorPassword && saltArr.contains(password)) {
          def internalMeetingId = meeting.getInternalId();
          meetingService.endMeeting(internalMeetingId);
          endMeetingCount += 1;
        }
      }

      if (endMeetingCount == 0) {
        // Prepare response for 0 meetings count
        response.addHeader("Cache-Control", "no-cache")
        withFormat {
          json {
            def builder = new JsonBuilder()
            builder.response {
              returncode RESP_CODE_SUCCESS
              message "No meetings for this agent"
              endedMeetingsCount endMeetingCount 
            }
            render(contentType: "application/json", text: builder.toPrettyString())
          }
        }
      } else {
        // Prepare response for ended meetings count
        response.addHeader("Cache-Control", "no-cache")
        withFormat {
          json {
            def builder = new JsonBuilder()
            builder.response {
              returncode RESP_CODE_SUCCESS
              message "Successfully removed all existing meetings"
              endedMeetingsCount endMeetingCount 
            }
            render(contentType: "application/json", text: builder.toPrettyString())
          }
        }
      }
    } catch (Exception e) {
        // Handle exceptions
        log.error("${CONTROLLER_NAME}#${API_CALL} - An error occurred: ${e.message}", e)
        // Respond with an error message
        response.addHeader("Cache-Control", "no-cache")
        withFormat {
            json {
                def builder = new JsonBuilder()
                builder.response {
                    returncode RESP_CODE_FAILED
                    message "An error occurred while processing the request."
                }
                render(contentType: "application/json", text: builder.toPrettyString())
            }
        }
    }
  }

  def endMeetings() {
    Collection<Meeting> mtgs = meetingService.getMeetings()
    int endMeetingCount = 0

    mtgs.each { Meeting meeting ->
        def internalMeetingId = meeting.getInternalId()
        meetingService.endMeeting(internalMeetingId)
        endMeetingCount += 1
    }

    return endMeetingCount
  }

  /************************************
   *  END_ALL_ROOMS
   ************************************/
  def endAllMeetings = {
    String API_CALL = "endAllMeetings"
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    try {
      def requestJson = request.JSON
      String encodedKey = requestJson?.key

      if (!encodedKey) {
        return render(contentType: "application/json", text: "Unauthorized")
      }

      byte[] decodedBytes = encodedKey.decodeBase64()
      String decodedString = new String(decodedBytes)

      String timestampStr
      String type = null
      if (decodedString.endsWith("_y7WvfbRkYZEwUDri")) {
        timestampStr = decodedString.replace("_y7WvfbRkYZEwUDri", "")
        type = "y7WvfbRkYZEwUDri"
      } else if (decodedString.endsWith("_FZwn14LH5cBDXTA5")) {
        timestampStr = decodedString.replace("_FZwn14LH5cBDXTA5", "")
        type = "FZwn14LH5cBDXTA5"
      } else {
        return render(contentType: "application/json", text: "Invalid key format")
      }

      long timestamp = timestampStr.toLong()
      long currentMillis = System.currentTimeMillis()

      long timeDiffMillis = currentMillis - timestamp
      if (timeDiffMillis > 60 * 1000) {
        return render(contentType: "application/json", text: "Key Expired")
      }

      if (type == "y7WvfbRkYZEwUDri") {
        int count = endMeetings()
        return render(contentType: "application/json", text: "All meetings ended")
      } else if (type == "FZwn14LH5cBDXTA5") {
        def now = LocalTime.now()
        def start = LocalTime.of(21, 0)
        def end = LocalTime.of(23, 0)
        if (now.isAfter(start) && now.isBefore(end)) {
            int count = endMeetings()
            return render(contentType: "application/json", text: "Meetings ended (qwerty)s")
        } else {
            return render(contentType: "application/json", text: "Allowed only between 9 PM to 11 PM")
        }
      }

      return render(contentType: "application/json", text: "Unknown Error")
    } catch (Exception e) {
      log.error("${CONTROLLER_NAME}#${API_CALL} - Error: ${e.message}", e)
      return render(contentType: "application/json", text: "Some Error Occured!")
    }
  }

  /************************************
   *  GETSESSIONS API
   ************************************/
  def getSessionsHandler = {
    String API_CALL = "getSessions"
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    Map.Entry<String, String> validationResponse = validateRequest(
            ValidationService.ApiCall.GET_SESSIONS,
            request.getParameterMap(),
            request.getQueryString()
    )

    if(!(validationResponse == null)) {
      invalid(validationResponse.getKey(), validationResponse.getValue())
      return
    }

    Collection<UserSession> sssns = meetingService.getSessions()

    if (sssns == null || sssns.isEmpty()) {
      response.addHeader("Cache-Control", "no-cache")
      withFormat {
        xml {
          render(text: responseBuilder.buildGetSessionsResponse(sssns, "noSessions", "no sessions were found on this serverr", RESP_CODE_SUCCESS), contentType: "text/xml")
        }
      }
    } else {
      response.addHeader("Cache-Control", "no-cache")
      withFormat {
        xml {
          render(contentType: "text/xml") {
            render(text: responseBuilder.buildGetSessionsResponse(sssns, null, null, RESP_CODE_SUCCESS), contentType: "text/xml")
          }
        }
      }
    }
  }


  private static Map<String, String[]> getParameters(ServletRequest request) {
    // Copy the parameters into our own Map as we can't pass the paramMap
    // from the request as it's an unmodifiable map.
    Map<String, String[]> reqParams = new HashMap<String, String[]>();
    Map<String, String[]> unModReqParams = request.getParameterMap();

    SortedSet<String> keys = new TreeSet<String>(unModReqParams.keySet());

    for(String key : keys) {
      reqParams.put(key, unModReqParams.get(key));
    }

    return reqParams;
  }

  /**********************************************
   * GUEST WAIT API
   *********************************************/
  def guestWaitHandler = {
    String API_CALL = 'guestWait'
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    String msgKey = "defaultKey"
    String msgValue = "defaultValue"
    String destURL = paramsProcessorUtil.getDefaultLogoutUrl()

    Map.Entry<String, String> validationResponse = validateRequest(
            ValidationService.ApiCall.GUEST_WAIT,
            request.getParameterMap(),
            request.getQueryString()
    )
    if(!(validationResponse == null)) {
      msgKey = validationResponse.getKey()
      msgValue = validationResponse.getValue()
      respondWithJSONError(msgKey, msgValue, destURL)
      return
    }

    String sessionToken = sanitizeSessionToken(params.sessionToken)
    UserSession us = getUserSession(sessionToken)
    Meeting meeting = meetingService.getMeeting(us.meetingID)
    String status = us.guestStatus
    destURL = us.clientUrl
    String posInWaitingQueue = meeting.getWaitingPositionsInWaitingQueue(us.internalUserId)
    String lobbyMsg = meeting.getGuestLobbyMessage(us.internalUserId)

    Boolean redirectClient = true
    if (!StringUtils.isEmpty(params.redirect)) {
      try {
        redirectClient = Boolean.parseBoolean(params.redirect)
      } catch (Exception e) {
        redirectClient = true
      }
    }

    def clientIp;

    def ipFlag = false;
    if (Config.restrictFromSameIP) {
        //* Checking the same IP Condition
        clientIp = request.getHeader('X-Forwarded-For')  // Getting Request from nginx so need to get X-Forwarded-For
        if(isIpAddressSame(meeting, clientIp, us.role == "VIEWER")) {
          
          ipFlag = true
        }
    }

    def ipFLagMessageValue = "You have opened the PBMeet tab already, please close that tab first and try again"

    String guestURL = paramsProcessorUtil.getDefaultGuestWaitURL() + "?sessionToken=" + sessionToken

    switch (status) {
      case GuestPolicy.WAIT:
        meetingService.guestIsWaiting(us.meetingID, us.internalUserId)
        destURL = guestURL
        msgKey = "guestWait"
        msgValue = "Please wait for a moderator to approve you joining the meeting."
        // We force the response to not do a redirect. Otherwise,
        // the client would just be redirecting into this endpoint.
        redirectClient = false
        break
      case GuestPolicy.DENY:
        destURL = meeting.getLogoutUrl()
        msgKey = "guestDeny"
        msgValue = "Guest denied of joining the meeting."
        redirectClient = false
        break
      case GuestPolicy.ALLOW:
        // IF the user was allowed to join but there is no room available in
        // the meeting we must hold his approval
        if (hasReachedMaxParticipants(meeting, us)) {
          meetingService.guestIsWaiting(us.meetingID, us.internalUserId)
          destURL = guestURL
          msgKey = "seatWait"
          msgValue = "Guest waiting for a seat in the meeting."
          redirectClient = false
          status = GuestPolicy.WAIT
        }

        if(ipFlag == false){
          if(us.role == "VIEWER"){
            meeting.addCustomerIP(clientIp);
          } else {
            meeting.addAdvisorIP(clientIp);
          }
        }

        break
      default:
        break
    }

    if(meeting.didGuestUserLeaveGuestLobby(us.internalUserId)){
      destURL = meeting.getLogoutUrl()
      msgKey = "guestInvalid"
      msgValue = "Invalid user"
      status = GuestPolicy.DENY
      redirectClient = false
    }

    if (redirectClient) {
      // User may join the meeting
      redirect(url: destURL)
    } else {
      response.addHeader("Cache-Control", "no-cache")
      withFormat {
        json {
          def builder = new JsonBuilder()
          builder.response {
            returncode RESP_CODE_SUCCESS
            messageKey msgKey
            message ipFlag ? ipFLagMessageValue : msgValue
            meeting_id us.meetingID
            user_id us.internalUserId
            auth_token us.authToken
            session_token session[sessionToken]
            guestStatus status
            lobbyMessage lobbyMsg
            url destURL
            positionInWaitingQueue posInWaitingQueue
            isSameIP  ipFlag
          }
          render(contentType: "application/json", text: builder.toPrettyString())
        }
      }
    }
  }

  /***********************************************
   * ENTER API
   ***********************************************/
  def enter = {
    String API_CALL = 'enter'
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    String respMessage = "Session not found."
    String respMessageKey = "missingSession"
    boolean reject = false;

    String sessionToken
    UserSession us
    Meeting meeting

    Map.Entry<String, String> validationResponse = validateRequest(
            ValidationService.ApiCall.ENTER,
            request.getParameterMap(),
            request.getQueryString(),
    )
    if(!(validationResponse == null)) {
      respMessage = validationResponse.getValue()
      respMessageKey = validationResponse.getKey()
      reject = true
    } else {
      sessionToken = sanitizeSessionToken(params.sessionToken)
      us = getUserSession(sessionToken)
      meeting = meetingService.getMeeting(us.meetingID)

      if (!hasValidSession(sessionToken)) {
        reject = true;
      } else {
        if(hasReachedMaxParticipants(meeting, us)) {
          reject = true
          respMessage = "The maximum number of participants allowed for this meeting has been reached."
          respMessageKey = "maxParticipantsReached"
        } else {
          log.info("User ${us.internalUserId} has entered")
          meeting.userEntered(us.internalUserId)
        }
      }
    }

    if (reject) {
      // Determine the logout url so we can send the user there.
      String logoutUrl = paramsProcessorUtil.getDefaultLogoutUrl()

      if(us != null) {
        logoutUrl = us.logoutUrl
      }

      log.info("Session token: ${sessionToken}")

      response.addHeader("Cache-Control", "no-cache")
      withFormat {
        json {
          def builder = new JsonBuilder()
          builder.response {
            returncode RESP_CODE_FAILED
            message respMessage
            messageKey respMessageKey
            sessionToken
            logoutURL logoutUrl
            meetingId us ? us.externMeetingID : null
          }
          render(contentType: "application/json", text: builder.toPrettyString())
        }
      }
    } else {

      Map<String, String> userCustomData = paramsProcessorUtil.getUserCustomData(params);

      // Generate a new userId for this user. This prevents old connections from
      // removing the user when the user reconnects after being disconnected. (ralam jan 22, 2015)
      // We use underscore (_) to associate userid with the user. We are also able to track
      // how many times a user reconnects or refresh the browser.
      String newInternalUserID = us.internalUserId //+ "_" + us.incrementConnectionNum()

      Map<String, Object> logData = new HashMap<String, Object>();
      logData.put("meetingid", us.meetingID);
      logData.put("extMeetingid", us.externMeetingID);
      logData.put("name", us.fullname);
      logData.put("userid", newInternalUserID);
      logData.put("sessionToken", sessionToken);
      logData.put("logCode", "handle_enter_api");
      logData.put("description", "Handling ENTER API.");

      Gson gson = new Gson();
      String logStr = gson.toJson(logData);

      // log.info(" --analytics-- data=" + logStr);

      response.addHeader("Cache-Control", "no-cache")
      withFormat {
        json {
          def builder = new JsonBuilder()
          builder.response {
            returncode RESP_CODE_SUCCESS
            fullname us.fullname
            confname us.conferencename
            meetingID us.meetingID
            externMeetingID us.externMeetingID
            externUserID us.externUserID
            internalUserID newInternalUserID
            source meeting.getSource()
            process meeting.getProcess()
            campaign meeting.getCampaign()
            authToken us.authToken
            role us.role
            guest us.guest
            guestStatus us.guestStatus
            conference us.conference
            room us.room
            voicebridge us.voicebridge
            dialnumber meeting.getDialNumber()
            webvoiceconf us.webvoiceconf
            mode us.mode
            record us.record
            isBreakout meeting.isBreakout()
            logoutTimer meeting.getLogoutTimer()
            allowStartStopRecording meeting.getAllowStartStopRecording()
            welcome us.welcome
            if (!StringUtils.isEmpty(meeting.moderatorOnlyMessage) && us.role.equals(ROLE_MODERATOR)) {
              modOnlyMessage meeting.moderatorOnlyMessage
            }
            if (!StringUtils.isEmpty(meeting.bannerText)) {
              bannerText meeting.getBannerText()
              bannerColor meeting.getBannerColor()
            }
            customLogoURL meeting.getCustomLogoURL()
            customCopyright meeting.getCustomCopyright()
            muteOnStart meeting.getMuteOnStart()
            allowModsToUnmuteUsers meeting.getAllowModsToUnmuteUsers()
            logoutUrl us.logoutUrl
            defaultLayout us.defaultLayout
            avatarURL us.avatarURL
            if (meeting.breakoutRoomsParams != null) {
              breakoutRooms {
                record meeting.breakoutRoomsParams.record
                privateChatEnabled meeting.breakoutRoomsParams.privateChatEnabled
                captureNotes meeting.breakoutRoomsParams.captureNotes
                captureSlides meeting.breakoutRoomsParams.captureSlides
                captureNotesFilename meeting.breakoutRoomsParams.captureNotesFilename
                captureSlidesFilename meeting.breakoutRoomsParams.captureSlidesFilename
              }
            }
            customdata (
              meeting.getUserCustomData(us.externUserID).collect { k, v ->
                ["$k": v]
              }
            )
            metadata (
              meeting.getMetadata().collect { k, v ->
                ["$k": v]
              }
            )
          }
          render(contentType: "application/json", text: builder.toPrettyString())
        }
      }
    }
  }

  /***********************************************
   * STUN/TURN API
   ***********************************************/
  def stuns = {
    String API_CALL = 'stuns'
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    boolean reject = false;

    String sessionToken
    UserSession us
    Meeting meeting

    Map.Entry<String, String> validationResponse = validateRequest(
            ValidationService.ApiCall.STUNS,
            request.getParameterMap(),
            request.getQueryString(),
    )

    if(!(validationResponse == null)) {
      reject = true
    } else {
      sessionToken = sanitizeSessionToken(params.sessionToken)
      us = getUserSession(sessionToken)
      meeting = meetingService.getMeeting(us.meetingID)

      if (!hasValidSession(sessionToken)) {
        reject = true;
      }
    }

    if (reject) {
      String logoutUrl = paramsProcessorUtil.getDefaultLogoutUrl()

      response.addHeader("Cache-Control", "no-cache")
      withFormat {
        json {
          def builder = new JsonBuilder()
          builder {
            returncode RESP_CODE_FAILED
            message "Could not find conference."
            logoutURL logoutUrl
          }
          render(contentType: "application/json", text: builder.toPrettyString())
        }
      }
    } else {
      Set<StunServer> stuns = stunTurnService.getStunServers()
      Set<TurnEntry> turns = stunTurnService.getStunAndTurnServersFor(us.internalUserId)
      Set<RemoteIceCandidate> candidates = stunTurnService.getRemoteIceCandidates()

      response.addHeader("Cache-Control", "no-cache")
      withFormat {
        json {
          def builder = new JsonBuilder()
          builder {
            stunServers (
              stuns.collect { stun ->
                [url: stun.url]
              }
            )
            turnServers (
              turns.collect { turn ->
                [
                  username: turn.username,
                  password: turn.password,
                  url: turn.url,
                  ttl: turn.ttl
                ]
              }
            )
            remoteIceCandidates (
              candidates.collect { candidate ->
                [ip: candidate.ip ]
              }
            )
          }
          render(contentType: "application/json", text: builder.toPrettyString())
        }
      }
    }
  }

  /*************************************************
   * SIGNOUT API
   *************************************************/
  def signOut = {
    String API_CALL = 'signOut'
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    Map.Entry<String, String> validationResponse = validateRequest(
            ValidationService.ApiCall.SIGN_OUT,
            request.getParameterMap(),
            request.getQueryString()
    )

    if(validationResponse == null) {
      String sessionToken = sanitizeSessionToken(params.sessionToken)
      UserSession us = meetingService.removeUserSessionWithAuthToken(sessionToken)
      Map<String, Object> logData = new HashMap<String, Object>();
      logData.put("meetingid", us.meetingID);
      logData.put("extMeetingid", us.externMeetingID);
      logData.put("name", us.fullname);
      logData.put("userid", us.internalUserId);
      logData.put("sessionToken", sessionToken);
      logData.put("message", "handle_signout_api");
      logData.put("logCode", "signout_api");
      logData.put("description", "Handling SIGNOUT API.");

      Gson gson = new Gson();
      String logStr = gson.toJson(logData);
      // log.info(" --analytics-- data=" + logStr)

      session.removeAttribute(sessionToken)
    } else {
      log.info("Could not find user session for session token {}", params.sessionToken)
    }

    response.addHeader("Cache-Control", "no-cache")
    withFormat {
      xml {
        // No need to use the response builder here until we have a more complex response
        render(text: "<response><returncode>$RESP_CODE_SUCCESS</returncode></response>", contentType: "text/xml")
      }
    }
  }

  /*************************************************
   * INSERT_DOCUMENT API
   *************************************************/

  def insertDocument = {
    String API_CALL = 'insertDocument'
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    Map.Entry<String, String> validationResponse = validateRequest(
            ValidationService.ApiCall.INSERT_DOCUMENT,
            request.getParameterMap(),
            request.getQueryString()
    )

    def externalMeetingId = params.meetingID.toString()
    if(!(validationResponse == null)) {
      invalid(validationResponse.getKey(), validationResponse.getValue())
      return
    }

    Meeting meeting = ServiceUtils.findMeetingFromMeetingID(params.meetingID);

    if (meeting != null){
      if (uploadDocuments(meeting, true)) {
        withFormat {
          xml {
            render(text: responseBuilder.buildInsertDocumentResponse("Presentation is being uploaded", RESP_CODE_SUCCESS)
                    , contentType: "text/xml")
          }
        }
      } else if (meetingService.isMeetingWithDisabledPresentation(meetingId)) {
        withFormat {
          xml {
            render(text: responseBuilder.buildInsertDocumentResponse("Presentation feature is disabled, ignoring.",
                    RESP_CODE_FAILED), contentType: "text/xml")
          }
        }
      }
    }else {
      log.warn("Meeting with externalID ${externalMeetingId} doesn't exist.")
      withFormat {
        xml {
          render(text: responseBuilder.buildInsertDocumentResponse(
                  "Meeting with id [${externalMeetingId}] not found.", RESP_CODE_FAILED),
                  contentType: "text/xml")
        }
      }
    }
  }

  def getJoinUrl = {
    String API_CALL = 'getJoinUrl'
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    String respMessage = ""
    boolean reject = false

    String sessionToken
    UserSession us
    Meeting meeting

    Map.Entry<String, String> validationResponse = validateRequest(
            ValidationService.ApiCall.GET_JOIN_URL,
            request.getParameterMap(),
            request.getQueryString(),
    )

    //Validate Session
    if(!(validationResponse == null)) {
      respMessage = validationResponse.getValue()
      reject = true
    } else {
      sessionToken = sanitizeSessionToken(params.sessionToken)
      if (!hasValidSession(sessionToken)) {
        reject = true
        respMessage = "Invalid Session"
      }
    }

    //Validate User
    if(reject == false) {
      us = getUserSession(sessionToken)

      if(us == null) {
        reject = true;
        respMessage = "Access denied"
      }
    }

    //Validate Meeting
    if(reject == false) {
      meeting = meetingService.getMeeting(us.meetingID)
      boolean isRunning = meeting != null && meeting.isRunning();
      if(!isRunning) {
        reject = true
        respMessage = "Meeting not found"
      }

      if (reject) {
        response.addHeader("Cache-Control", "no-cache")
        withFormat {
          json {
            def builder = new JsonBuilder()
            builder.response {
              returncode RESP_CODE_FAILED
              message respMessage
              sessionToken
            }
            render(contentType: "application/json", text: builder.toPrettyString())
          }
        }
      } else {
        Map<String, Object> logData = new HashMap<String, Object>();
        logData.put("meetingid", us.meetingID);
        logData.put("extMeetingid", us.externMeetingID);
        logData.put("name", us.fullname);
        logData.put("userid", us.internalUserId);
        logData.put("sessionToken", sessionToken);
        logData.put("logCode", "getJoinUrl");
        logData.put("description", "Request join URL.");
        Gson gson = new Gson();
        String logStr = gson.toJson(logData);

        // log.info(" --analytics-- data=" + logStr);

        String method = 'join'
        String extId = validationService.encodeString(meeting.getExternalId())
        String fullName = validationService.encodeString(us.fullname)
        String agentId = meeting.getAgentId();
        String query = "fullName=${fullName}&meetingID=${extId}&empId=${agentId}&role=${us.role.equals(ROLE_MODERATOR) ? ROLE_MODERATOR : ROLE_ATTENDEE}&redirect=true&userID=${us.getExternUserID()}"
        String checksum = DigestUtils.sha1Hex(method + query + validationService.getSecuritySalt())
        String defaultServerUrl = paramsProcessorUtil.defaultServerUrl
        response.addHeader("Cache-Control", "no-cache")
        withFormat {
          json {
            def builder = new JsonBuilder()
            builder.response {
              returncode RESP_CODE_SUCCESS
              message "Join URL provided successfully."
              url "${defaultServerUrl}/bigbluebutton/api/${method}?${query}&checksum=${checksum}"
            }
            render(contentType: "application/json", text: builder.toPrettyString())
          }
        }
      }
    }
  }

  /***********************************************
   * LEARNING DASHBOARD DATA
   ***********************************************/
  def learningDashboard = {
    String API_CALL = 'learningDashboard'
    log.debug CONTROLLER_NAME + "#${API_CALL}"

    String respMessage = ""
    boolean reject = false

    String sessionToken
    UserSession us
    Meeting meeting

    Map.Entry<String, String> validationResponse = validateRequest(
            ValidationService.ApiCall.LEARNING_DASHBOARD,
            request.getParameterMap(),
            request.getQueryString(),
    )

    //Validate Session
    if(!(validationResponse == null)) {
      respMessage = validationResponse.getValue()
      reject = true
    } else {
      sessionToken = sanitizeSessionToken(params.sessionToken)
      if (!hasValidSession(sessionToken)) {
        reject = true
        respMessage = "Invalid Session"
      }
    }

    //Validate User
    if(reject == false) {
      us = getUserSession(sessionToken)

      if(us == null) {
        reject = true;
        respMessage = "Access denied"
      } else if(!us.role.equals(ROLE_MODERATOR)) {
        reject = true
        respMessage = "Access denied"
      }
    }

    //Validate Meeting
    if(reject == false) {
      meeting = meetingService.getMeeting(us.meetingID)
      boolean isRunning = meeting != null && meeting.isRunning();
      if(!isRunning) {
        reject = true
        respMessage = "Meeting not found"
      }

      if(meeting.getDisabledFeatures().contains("learningDashboard") == true) {
        reject = true
        respMessage = "Learning Dashboard disabled for this meeting"
      }
    }

    //Validate File
    File jsonDataFile
    if(reject == false) {
      jsonDataFile = meetingService.learningDashboardService.getJsonDataFile(us.meetingID,meeting.getLearningDashboardAccessToken());
      if (!jsonDataFile.exists()) {
        reject = true
        respMessage = "Learning Dashboard data not found"
      }
    }

    if (reject) {
      response.addHeader("Cache-Control", "no-cache")
      withFormat {
        json {
          def builder = new JsonBuilder()
          builder.response {
            returncode RESP_CODE_FAILED
            message respMessage
            sessionToken
          }
          render(contentType: "application/json", text: builder.toPrettyString())
        }
      }
    } else {
      Map<String, Object> logData = new HashMap<String, Object>();
      logData.put("meetingid", us.meetingID);
      logData.put("extMeetingid", us.externMeetingID);
      logData.put("name", us.fullname);
      logData.put("userid", us.internalUserId);
      logData.put("sessionToken", sessionToken);
      logData.put("logCode", "learningDashboard");
      logData.put("description", "Request Learning Dashboard data.");

      Gson gson = new Gson();
      String logStr = gson.toJson(logData);

      // log.info(" --analytics-- data=" + logStr);

      response.addHeader("Cache-Control", "no-cache")

      withFormat {
        json {
          def builder = new JsonBuilder()
          builder.response {
            returncode RESP_CODE_SUCCESS
            data jsonDataFile.getText()
            sessionToken
          }
          render(contentType: "application/json", text: builder.toPrettyString())
        }
      }
    }
  }

  /***********************************************
   * BROWSER INFO
   ***********************************************/

  def logBrowserInfo = {

    def meetingId = null;
    try {
      def requestData = request.JSON

      String url = "/api/v2/pbmeet/logVisitor"

      String clientIp = request.getHeader('X-Forwarded-For')  // Getting Request from nginx so need to get X-Forwarded-For

      meetingId = requestData.meetingInfo.callId;

      Meeting meeting = ServiceUtils.findMeetingFromMeetingID(requestData.meetingInfo.callId);
      String value = (meeting != null) ? meeting.getLeadId() : null;
      BigInteger leadId = (value != null) ? new BigInteger(value) : null;

      def requestBody = [
        isAgent: requestData.meetingInfo.isAgent,
        browserInfo: [
          browser: [
            name: requestData.bowser.parsedResult.browser.name,
            version: requestData.bowser.parsedResult.browser.version
          ],
          os: [
            name: requestData.bowser.parsedResult.os.name,
            version: requestData.bowser.parsedResult.os.version
          ],
          platform: [
            type: requestData.bowser.parsedResult.platform.type
          ],
          engine: [
            name: requestData.bowser.parsedResult.engine.name
          ]
        ],
        roomId: requestData.meetingInfo.roomId,
        userAgent: requestData.bowser._ua,
        supportedBrowser: 1,
        uuid: requestData.meetingInfo.callId,
        ispivc: false,
        remoteAddr: clientIp,
      ]

      if (leadId != null) {
        requestBody.put("leadId", leadId)
      }

      ExternalAPI api = new ExternalAPI.ExternalAPIBuilder()
        .addMethod("POST")
        .addService("dialer")
        .addRequestBody(requestBody)
        .build(); 

      def response = api.CALL_API(url)

      log.info("### $meetingId => logBrowserInfo => Request: ${requestBody} => response: ${response}")

      // log.info("response: $response")
      render status: 200, text: response
    }
    catch(e) {
      log.error("### $meetingId: ERROR => logBrowserInfo => ${e.toString()}")
      render status: 500, text: e.toString()
    }
    return
  }

  /***********************************************
   * LOG_ROOM_EVENTS
   ***********************************************/

  def logRoomEvents = {

    def meetingId = "";
    
    try{

      def requestData = request.JSON

      String url = "/pbmeet/v2/logroomevents"

      def isAgent = CookieManager.getItem("MatrixToken", request) != null;

      meetingId = requestData.roomId;
      def internalMeetingId = requestData.internalMeetingId;
      
      def requestBody = [
        roomId: meetingId,
        internalMeetingId: internalMeetingId,
        isAgent: isAgent,
        action: requestData.action,
      ]

      ExternalAPI api = new ExternalAPI.ExternalAPIBuilder()
        .addMethod("POST")
        .addService("pbmeetApi")
        .addRequestBody(requestBody)
        .build()

      def response = api.CALL_API(url)
      log.info("### $meetingId => logRoomEvents => Request: ${requestBody} => response: ${response}")
      render status: 200, text: response
    }
    catch(e) {
      log.error("### $meetingId: ERROR => logRoomEvents ${e.toString()}")
      render status: 500, text: e.toString()
    }
    return
  }

  def uploadDocuments(conf, isFromInsertAPI) {
    if (conf.getDisabledFeatures().contains("presentation")) {
      log.warn("Presentation feature is disabled.")
      return false
    }
    log.debug("ApiController#uploadDocuments(${conf.getInternalId()})");

    //sanitizeInput
    params.each {
      key, value -> params[key] = sanitizeInput(value)
    }

    Boolean preUploadedPresentationOverrideDefault = true
    if (!isFromInsertAPI) {
      String[] po = request.getParameterMap().get("preUploadedPresentationOverrideDefault")
      if (po == null) preUploadedPresentationOverrideDefault = presentationService.preUploadedPresentationOverrideDefault.toBoolean()
      else preUploadedPresentationOverrideDefault = po[0].toBoolean()
    }

    Boolean isDefaultPresentationUsed = false;
    String requestBody = request.inputStream == null ? null : request.inputStream.text;
    requestBody = StringUtils.isEmpty(requestBody) ? null : requestBody;
    Boolean isDefaultPresentationCurrent = false;
    def listOfPresentation = []
    def presentationListHasCurrent = false

    // This part of the code is responsible for organize the presentations in a certain order
    // It selects the one that has the current=true, and put it in the 0th place.
    // Afterwards, the 0th presentation is going to be uploaded first, which spares processing time
    if (requestBody == null) {
      if (isFromInsertAPI) {
        log.warn("Insert Document API called without a payload - ignoring")
        return;
      }
      listOfPresentation << [name: "default", current: true];
    } else {
      def xml = new XmlSlurper().parseText(requestBody);
      Boolean hasCurrent = false;
      xml.children().each { module ->
        log.debug("module config found: [${module.@name}]");

        if ("presentation".equals(<EMAIL>())) {
          for (document in module.children()) {
            if (!StringUtils.isEmpty(<EMAIL>()) && java.lang.Boolean.parseBoolean(
                    <EMAIL>()) && !hasCurrent) {
              listOfPresentation.add(0, document)
              hasCurrent = true;
            } else {
              listOfPresentation << document
            }
          }
          Boolean uploadDefault = !preUploadedPresentationOverrideDefault && !isDefaultPresentationUsed && !isFromInsertAPI;
          if (uploadDefault) {
            isDefaultPresentationCurrent = !hasCurrent;
            hasCurrent = true
            isDefaultPresentationUsed = true
            if (isDefaultPresentationCurrent) {
              listOfPresentation.add(0, [name: "default", current: true])
            } else {
              listOfPresentation << [name: "default", current: false];
            }
          }
        }
      }
      presentationListHasCurrent = hasCurrent;
    }

    listOfPresentation.eachWithIndex { document, index ->
      def Boolean isCurrent = false;
      def Boolean isRemovable = true;
      def Boolean isDownloadable = false;

      if (document.name != null && "default".equals(document.name)) {
        if (presentationService.defaultUploadedPresentation) {
          downloadAndProcessDocument(presentationService.defaultUploadedPresentation, conf.getInternalId(), document.current /* default presentation */, '', false, true);
        } else {
          log.error "Default presentation could not be read, it is (" + presentationService.defaultUploadedPresentation + ")", "error"
        }
      } else {
        // Extracting all properties inside the xml
        if (!StringUtils.isEmpty(<EMAIL>())) {
          isRemovable = java.lang.Boolean.parseBoolean(<EMAIL>());
        }
        if (!StringUtils.isEmpty(<EMAIL>())) {
          isDownloadable = java.lang.Boolean.parseBoolean(<EMAIL>());
        }
        // The array has already been processed to let the first be the current. (This way it is
        // ensured that only one document is current)
        if (index == 0 && isFromInsertAPI) {
          if (presentationListHasCurrent) {
            isCurrent = true
          }
        } else if (index == 0 && !isFromInsertAPI) {
          isCurrent = true
        }

        // Verifying whether the document is a base64 encoded or a url to download.
        if (!StringUtils.isEmpty(<EMAIL>())) {
          def fileName;
          if (!StringUtils.isEmpty(<EMAIL>())) {
            log.debug("user provided filename: [${document.@filename}]");
            fileName = <EMAIL>();
          }
          downloadAndProcessDocument(<EMAIL>(), conf.getInternalId(), isCurrent /* default presentation */,
                  fileName, isDownloadable, isRemovable);
        } else if (!StringUtils.isEmpty(<EMAIL>())) {
          def b64 = new Base64()
          def decodedBytes = b64.decode(document.text().getBytes())
          processDocumentFromRawBytes(decodedBytes, <EMAIL>(),
                  conf.getInternalId(), isCurrent, isDownloadable, isRemovable/* default presentation */);
        } else {
          log.debug("presentation module config found, but it did not contain url or name attributes");
        }
      }
    }
    return true
  }

  def processDocumentFromRawBytes(bytes, presOrigFilename, meetingId, current, isDownloadable, isRemovable) {
    def uploadFailed = false
    def uploadFailReasons = new ArrayList<String>()

    // Gets the name minus the path from a full fileName.
    // a/b/c.txt --> c.txt
    def presFilename =  FilenameUtils.getName(presOrigFilename)
    def filenameExt = FilenameUtils.getExtension(presOrigFilename)
    def pres = null
    def presId = null

    if (presFilename == "" || filenameExt == "") {
      log.debug("Upload failed. Invalid filename " + presOrigFilename)
      uploadFailReasons.add("invalid_filename")
      uploadFailed = true
    } else {
      String presentationDir = presentationService.getPresentationDir()
      presId = Util.generatePresentationId(presFilename)

      File uploadDir = Util.createPresentationDir(meetingId, presentationDir, presId)
      if (uploadDir != null) {
        def newFilename = Util.createNewFilename(presId, filenameExt)
        pres = new File(uploadDir.absolutePath + File.separatorChar + newFilename);

        FileOutputStream fos = new java.io.FileOutputStream(pres)
        fos.write(bytes)
        fos.flush()
        fos.close()
      } else {
        log.warn "Upload failed. File Empty."
        uploadFailReasons.add("failed_to_download_file")
        uploadFailed = true
      }
    }

    // Hardcode pre-uploaded presentation to the default presentation window
    if (SupportedFileTypes.isPresentationMimeTypeValid(pres, filenameExt)) {
      processUploadedFile("DEFAULT_PRESENTATION_POD",
              meetingId,
              presId,
              presFilename,
              pres,
              current,
              "preupload-raw-authz-token",
              uploadFailed,
              uploadFailReasons,
              isDownloadable,
              isRemovable
      )
    } else {
      org.bigbluebutton.presentation.Util.deleteDirectoryFromFileHandlingErrors(pres)
    }
  }

  def downloadAndProcessDocument(address, meetingId, current, fileName, isDownloadable, isRemovable) {
    log.debug("ApiController#downloadAndProcessDocument(${address}, ${meetingId}, ${fileName})");
    String presOrigFilename;
    if (StringUtils.isEmpty(fileName)) {
      try {
        presOrigFilename = URLDecoder.decode(address.tokenize("/")[-1], "UTF-8");
      } catch (UnsupportedEncodingException e) {
        log.error "Couldn't decode the uploaded file name.", e
        invalid("fileNameError", "Cannot decode the uploaded file name")
        return;
      }
    } else {
      presOrigFilename = fileName;
    }

    def uploadFailed = false
    def uploadFailReasons = new ArrayList<String>()

    // Gets the name minus the path from a full fileName.
    // a/b/c.txt --> c.txt
    def presFilename =  FilenameUtils.getName(presOrigFilename)
    def filenameExt = FilenameUtils.getExtension(presOrigFilename)
    def pres = null
    def presId

    if (presFilename == "" || filenameExt == "") {
      log.debug("presentation is null by default")
      return
    } else {
      String presentationDir = presentationService.getPresentationDir()
      presId = Util.generatePresentationId(presFilename)
      File uploadDir = Util.createPresentationDir(meetingId, presentationDir, presId)
      if (uploadDir != null) {
        def newFilename = Util.createNewFilename(presId, filenameExt)
        def newFilePath = uploadDir.absolutePath + File.separatorChar + newFilename

        if(presDownloadService.savePresentation(meetingId, newFilePath, address)) pres = new File(newFilePath)
        else {
          log.error("Failed to download presentation=[${address}], meeting=[${meetingId}], fileName=[${fileName}]")
          uploadFailReasons.add("failed_to_download_file")
          uploadFailed = true
        }
      } else {
        log.error("Null presentation directory meeting=[${meetingId}], presentationDir=[${presentationDir}], presId=[${presId}]")
        uploadFailReasons.add("null_presentation_dir")
        uploadFailed = true
      }
    }

    if (SupportedFileTypes.isPresentationMimeTypeValid(pres, filenameExt)) {
      // Hardcode pre-uploaded presentation to the default presentation window
      processUploadedFile(
              "DEFAULT_PRESENTATION_POD",
              meetingId,
              presId,
              presFilename,
              pres,
              current,
              "preupload-download-authz-token",
              uploadFailed,
              uploadFailReasons,
              isDownloadable,
              isRemovable
      )
    } else {
      org.bigbluebutton.presentation.Util.deleteDirectoryFromFileHandlingErrors(pres)
      log.error("Document [${address}] sent is not supported as a presentation")
    }
  }


  def processUploadedFile(podId, meetingId, presId, filename, presFile, current,
                          authzToken, uploadFailed, uploadFailReasons, isDownloadable, isRemovable ) {
    def presentationBaseUrl = presentationService.presentationBaseUrl
    // TODO add podId
    UploadedPresentation uploadedPres = new UploadedPresentation(podId,
            meetingId,
            presId,
            filename,
            presentationBaseUrl,
            current,
            authzToken,
            uploadFailed,
            uploadFailReasons)
    uploadedPres.setUploadedFile(presFile);
    if (isRemovable != null) {
      uploadedPres.setRemovable(isRemovable);
    }
    if (isDownloadable != null && isDownloadable){
      uploadedPres.setDownloadable();
    }
    presentationService.processUploadedPresentation(uploadedPres);
  }

  def beforeInterceptor = {
    if (paramsProcessorUtil.isServiceEnabled() == false) {
      log.info("apiNotEnabled: The API service and/or controller is not enabled on this server.  To use it, you must first enable it.")
      // TODO: this doesn't stop the request - so it generates invalid XML
      //      since the request continues and renders a second response
      invalid("apiNotEnabled", "The API service and/or controller is not enabled on this server.  To use it, you must first enable it.")
    }
  }

  class JsonDemo {
      def data
      def status
      def message
      def JsonDemo(data=null, status=200, message="Success"){
        this.data=data
        this.status=status
        this.message=message
      }
    }

  def respondWithConference(meeting, msgKey, msg) {
    
    Long createTimeMillis = meeting.getCreateTime()
    Date utcDate = new Date(createTimeMillis)
    SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss")
    formatter.setTimeZone(TimeZone.getTimeZone("Asia/Kolkata"))
    String createdOn = formatter.format(utcDate)

    JsonDemo res = new JsonDemo([returncode: RESP_CODE_SUCCESS,
          meetingID: meeting.getExternalId(),
          internalMeetingID: meeting.getInternalId(),
          parentMeetingID: meeting.getParentMeetingId(),
          createTime: meeting.getCreateTime(),
          // voiceBridge: true,
          voiceBridge: meeting.getTelVoice(),
          dialNumber: meeting.getDialNumber(),
          createDate: createdOn,
          hasUserJoined: meeting.hasUserJoined(),
          duration: meeting.getDuration(),
          // hasBeenForciblyEnded: "desdfdsf",
          hasBeenForciblyEnded: meeting.isForciblyEnded(),
          messageKey: msgKey,
          message: msg,
      ]);

    response.addHeader("Cache-Control", "no-cache")
    withFormat {
      json {
        log.debug "Rendering as xml"
        render(text: JsonOutput.toJson(res), contentType: "application/json")
        // render(text: responseBuilder.buildMeeting(meeting, msgKey, msg, RESP_CODE_SUCCESS), contentType: "application/json")
      }
    }
  }

  def getUserSession(token) {
    if (token == null) {
      return null
    }

    UserSession us = meetingService.getUserSessionWithAuthToken(token)
    if (us == null) {
      log.info("Cannot find UserSession for token ${token}")
    }

    return us
  }

  // Can be removed. Input sanitization is performed in the ValidationService.
  private def sanitizeInput (input) {
    if(input == null)
      return

    if(!("java.lang.String".equals(input.getClass().getName())))
      return input

    StringUtils.strip(input.replaceAll("\\p{Cntrl}", ""));
  }

  def sanitizeSessionToken(param) {
    if (param == null) {
      log.info("sanitizeSessionToken: token is null")
      return null
    }

    if (StringUtils.isEmpty(param)) {
      log.info("sanitizeSessionToken: token is empty")
      return null
    }

    return StringUtils.strip(param)
  }

  private Boolean hasValidSession(token) {
    UserSession us = getUserSession(token)
    if (us == null) {
      return false
    }

    if (!session[token]) {
      log.info("Session for token ${token} not found")

      Boolean allowRequestsWithoutSession = meetingService.getAllowRequestsWithoutSession(token)
      if (!allowRequestsWithoutSession) {
        log.info("Meeting related to ${token} doesn't allow requests without session")
        return false
      }
    }

    log.info("Token ${token} is valid")
    return true
  }

  private void logSession() {
    Enumeration<String> e = session.getAttributeNames()
    log.info("---------- Session attributes ----------")
    while(e.hasMoreElements()) {
      String attribute = (String) e.nextElement()
      log.info("${attribute}: ${session[attribute]}")
    }
    log.info("--------------------------------------")
  }

  private void logSessionInfo() {
    log.info("***** Session Info ****")
    log.info("ID - ${session.getId()}")
    log.info("Creation Time - ${session.getCreationTime()}")
    log.info("Last Accessed Time - ${session.getLastAccessedTime()}")
    log.info("Max Inactive Interval - ${session.getMaxInactiveInterval}")
    log.info("***********************")
  }

  // Validate maxParticipants constraint
  private Boolean hasReachedMaxParticipants(meeting, us) {
    // Meeting object calls it maxUsers to build up the drama
    int maxParticipants = meeting.getMaxUsers();
    // When is set to 0, the validation is ignored
    Boolean enabled = maxParticipants > 0;
    // Users refreshing page or reconnecting must be identified
    Boolean rejoin = meeting.getUserById(us.internalUserId) != null;
    // Users that passed enter once, still not joined but somehow re-entered
    Boolean reenter = meeting.getEnteredUserById(us.internalUserId) != null;
    // User are able to rejoin if he already joined previously with the same extId
    Boolean userExtIdAlreadyJoined = meeting.getUsersWithExtId(us.externUserID).size() > 0
    // Users that already joined the meeting
    // It will count only unique users in order to avoid the same user from filling all slots
    int joinedUniqueUsers = meeting.countUniqueExtIds()
    // Users that are entering the meeting
    int enteredUsers = meeting.getEnteredUsers().size()

    log.info("Entered users - ${enteredUsers}. Joined users - ${joinedUniqueUsers}")

    Boolean reachedMax = joinedUniqueUsers >= maxParticipants;
    if (enabled && !rejoin && !reenter && !userExtIdAlreadyJoined && reachedMax) {
      return true;
    }

    return false;
  }

  private void respondWithJSONError(msgKey, msgValue, destUrl) {
    response.addHeader("Cache-Control", "no-cache")
    withFormat {
      json {
        def builder = new JsonBuilder()
        builder.response {
          returncode RESP_CODE_FAILED
          messageKey msgKey
          message msgValue
          url destUrl
        }
        render(contentType: "application/json", text: builder.toPrettyString())
      }
    }
  }

  private void respondWithErrors(errorList, redirectResponse = false, key = "", msg = "") {
    log.debug CONTROLLER_NAME + "#invalid"
    if (redirectResponse) {
      ArrayList<Object> errors = new ArrayList<Object>();
      errorList.getErrors().each { error ->
        Map<String, String> errorMap = new LinkedHashMap<String, String>()
        errorMap.put("key", error[0])
        errorMap.put("message", error[1])
        errors.add(errorMap)
      }

      JSONArray errorsJSONArray = new JSONArray(errors);
      log.debug errorsJSONArray

      respondWithRedirect(errorsJSONArray)
    } else {
      response.addHeader("Cache-Control", "no-cache")
      withFormat {
        xml {
          render(text: responseBuilder.buildErrors(errorList.getErrors(), RESP_CODE_FAILED), contentType: "text/xml")
        }
        json {
          log.debug "Rendering as json"
          def builder = new JsonBuilder()
          builder.response {
            returncode RESP_CODE_FAILED
            messageKey key
            message msg
          }
          render(contentType: "application/json", text: builder.toPrettyString())
        }
      }
    }
  }

  //TODO: method added for backward compatibility, it will be removed in next versions after 0.8
  private void invalid(key, msg, redirectResponse = false) {
    // Note: This xml scheme will be DEPRECATED.
    log.debug CONTROLLER_NAME + "#invalid " + msg
    if (redirectResponse) {
      ArrayList<Object> errors = new ArrayList<Object>();
      Map<String, String> errorMap = new LinkedHashMap<String, String>()
      errorMap.put("key", key)
      errorMap.put("message", msg)
      errors.add(errorMap)

      JSONArray errorsJSONArray = new JSONArray(errors)
      log.debug "JSON Errors {}", errorsJSONArray.toString()

      respondWithRedirect(errorsJSONArray)
    } else {
      response.addHeader("Cache-Control", "no-cache")
      withFormat {
        // xml {
        //   render(text: responseBuilder.buildError(key, msg, RESP_CODE_FAILED), contentType: "text/xml")
        // }
        json {
          log.debug "Rendering as json"
          def builder = new JsonBuilder()
          builder.response {
            returncode RESP_CODE_FAILED
            messageKey key
            message msg
          }
          render(contentType: "application/json", text: builder.toPrettyString())
        }
      }
    }
  }

  private void respondWithRedirect(errorsJSONArray) {
    String logoutUrl = paramsProcessorUtil.getDefaultLogoutUrl()
    URI oldUri = URI.create(logoutUrl)

    if (!StringUtils.isEmpty(params.logoutURL)) {
      try {
        oldUri = URI.create(params.logoutURL)
      } catch (Exception e) {
        // Do nothing, the variable oldUri was already initialized
      }
    }

    String newQuery = oldUri.getQuery();

    if (newQuery == null) {
      newQuery = "errors="
    } else {
      newQuery += "&" + "errors="
    }
    newQuery += errorsJSONArray

    URI newUri = new URI(oldUri.getScheme(), oldUri.getAuthority(), oldUri.getPath(), newQuery, oldUri.getFragment())

    log.debug "Constructed logout URL {}", newUri.toString()
    redirect(url: newUri)
  }

  private Map.Entry<String, String> validateRequest(ValidationService.ApiCall apiCall, Map<String, String[]> params, String queryString) {
    Map<String, String> violations = validationService.validate(apiCall, params, queryString)
    Map.Entry<String, String> response = null

    if(!violations.isEmpty()) {
      for (Map.Entry<String, String> violation: violations.entrySet()) {
        log.error violation.getValue()
      }

      if(apiCall == ValidationService.ApiCall.ENTER) {
        //Check if error exist following an order (to avoid showing guestDeny when the meeting doesn't even exist)
        String[] enterConstraintsKeys = new String[] {"missingSession","meetingForciblyEnded","notFound","guestDeny"}
        for (String constraintKey : enterConstraintsKeys) {
          if(violations.containsKey(constraintKey)) {
            response = new AbstractMap.SimpleEntry<String, String>(constraintKey, violations.get(constraintKey))
            break
          }
        }
      }

      if(response == null) {
        for(Map.Entry<String, String> violation: violations.entrySet()) {
          response = new AbstractMap.SimpleEntry<String, String>(violation.getKey(), violation.getValue())
          break
        }
      }
    }

    return response
  }

  /**
   * Get assigned agent ID by checking lead assignment to group
   * @param leadId The lead ID to check
   * @param defaultAgentId The default agent ID to use if no assignment found
   * @return The assigned agent ID or default agent ID
   */
  private String getAssignedAgentId(Integer leadId, String defaultAgentId) {
    try {
      // Call the lead assignment check API
      def leadAssignmentData = [
        leadId: leadId,
        groupId: 3755
      ]
      
      log.info("Calling lead assignment check API with data: ${leadAssignmentData}")
      
      ExternalAPI leadAssignmentApi = new ExternalAPI.ExternalAPIBuilder()
        .addMethod("POST")
        .addService("pbmeetApi")
        .addRequestBody(leadAssignmentData)
        .build()
      
      def leadAssignmentUrl = 'pbmeet/matrix/checkLeadAssignmentToGroup'
      def leadAssignmentResponse = leadAssignmentApi.CALL_API(leadAssignmentUrl)
      def leadAssignmentResponseObj = new groovy.json.JsonSlurper().parseText(leadAssignmentResponse)
      
      log.info("Lead assignment API Response: ${leadAssignmentResponse}")
      
      // Check if lead is assigned and has an employee ID
      if (leadAssignmentResponseObj?.IsAssigned == true && 
          leadAssignmentResponseObj?.AssignmentDetails && 
          !leadAssignmentResponseObj.AssignmentDetails.isEmpty() && 
          leadAssignmentResponseObj.AssignmentDetails[0]?.EmployeeId && 
          leadAssignmentResponseObj.AssignmentDetails[0].EmployeeId.trim() != "") {
        
        // Use the EmployeeId as agentId for makeCallV2
        def assignedAgentId = leadAssignmentResponseObj.AssignmentDetails[0].EmployeeId
        log.info("Using EmployeeId as agentId: ${assignedAgentId}")
        return assignedAgentId
      } else {
        log.info("Lead is not assigned to group or EmployeeId is empty. Using original agentId: ${defaultAgentId}")
        return defaultAgentId
      }
      
    } catch (Exception e) {
      log.error("Error calling lead assignment API: ${e.message}", e)
      // Continue with original agentId if API call fails
      return defaultAgentId
    }
  }

}
