#
# BigBlueButton open source conferencing system - http://www.bigbluebutton.org/
#
# Copyright (c) 2012 BigBlueButton Inc. and by respective authors (see below).
#
# This program is free software; you can redistribute it and/or modify it under the
# terms of the GNU Lesser General Public License as published by the Free Software
# Foundation; either version 3.0 of the License, or (at your option) any later
# version.
#
# BigBlueButton is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE. See the GNU Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public License along
# with BigBlueButton; if not, see <http://www.gnu.org/licenses/>.
#

#
# These are the default properites for BigBlueButton Web application

# Default loglevel.
appLogLevel=DEBUG

#----------------------------------------------------
# Directory where BigBlueButton stores uploaded slides
presentationDir=/var/bigbluebutton

#----------------------------------------------------
# Directory where ImageMagick's convert executable is located
imageMagickDir=/usr/bin

#----------------------------------------------------
# Executable for presentation checker
presCheckExec=/usr/share/prescheck/prescheck.sh

#----------------------------------------------------
# Skip Office doc conversion pre-check. Attempt to convert
# Office doc to PDF right away.
skipOfficePrecheck=true

#----------------------------------------------------
# Executable for presentation office conversion
presOfficeConversionExec=/usr/share/bbb-libreoffice-conversion/convert.sh

#----------------------------------------------------
# These will be copied in cases where the conversion process
# fails to generate a slide from the uploaded presentation
BLANK_PRESENTATION=/usr/share/bigbluebutton/blank/blank-presentation.pdf
BLANK_THUMBNAIL=/usr/share/bigbluebutton/blank/blank-thumb.png
BLANK_PNG=/usr/share/bigbluebutton/blank/blank-png.png
BLANK_SVG=/usr/share/bigbluebutton/blank/blank-svg.svg

#----------------------------------------------------
# Number of minutes the conversion should take. If it takes
# more than this time, cancel the conversion process.
maxConversionTime=5

#----------------------------------------------------
# Maximum number of pages allowed for an uploaded presentation (default 100).
maxNumPages=200

#----------------------------------------------------
# Maximum file size for an uploaded presentation (default 30MB).
maxFileSizeUpload=30000000

#----------------------------------------------------
# Maximum allowed number of place object tags in generated svg, if exceeded the conversion will fallback to full BMP (default 800)
placementsThreshold=800

# Maximum allowed number of bitmap images in generated svg, if exceeded the conversion will fallback to full BMP (default 800)
imageTagThreshold=800

#------------------------------------
# Number of threads in the pool to do the presentation conversion.
#------------------------------------
numConversionThreads=5

#------------------------------------
# Number of threads to process file uploads
#------------------------------------
numFileProcessorThreads=2

#------------------------------------
# Timeout(secs) to wait for pdf to svg conversion (timeout for each tool called during the process)
#------------------------------------
svgConversionTimeout=60

#------------------------------------
# pdfFonts is used to detect whether PDF contains text with font Type 3
# it is also used to check if the pdf has some problem to be opened (timeout) and abort the conversion if so
# Configuration for pdfFonts Timeout(secs) and Max number of attempts
#------------------------------------
pdfFontsTimeout=3
maxNumberOfAttemptsForPdfFonts=3

#------------------------------------
# Presentation resolution, in PPI (will be set to generated svg)
#------------------------------------
svgPresentationResolutionPpi=300

#------------------------------------
# Force conversion of slides to PNG before converting to SVG
## Experimental - this option might be removed in next releases
## This will solve problems like reported in issue #8835
## Disabled by default as it can affect the quality in zoom
#------------------------------------
forceRasterizeSlides=false

#------------------------------------
# Presentation will be resized to this width (in pixels) when rasterizing (converting to PNG)
## Applied in these situations:
##  a) the source can't be converted directly to SVG ;
##  b) option "forceRasterizeSlides" is defined as true ;
## To disable this constraint (and keep source resolution) define this property as 0.
#------------------------------------
pngWidthRasterizedSlides=2048


#------------------------------------
# Timeout(secs) to wait for conversion script execution
#------------------------------------
officeToPdfConversionTimeout=60

#------------------------------------
# Max concurrent of conversion script execution
#------------------------------------
officeToPdfMaxConcurrentConversions=4

#----------------------------------------------------
# Additional conversion of the presentation slides to PNG
# to be used in the IOS mobile client
generatePngs=false
pngSlideWidth=1200

# Default number of digits for voice conference users joining through the PSTN.
defaultNumDigitsForTelVoice=5

#----------------------------------------------------
# Maximum image resolution, if image exceeds this limit it will be resized
maxImageWidth=2048
maxImageHeight=1536

#----------------------------------------------------
# Configuration for large PDF, 14 MB by default, if bigger it will be analysed during the conversion process
bigPdfSize=14000000

# The maximum allowed page size for PDF files exceeding the 'pdfCheckSize' value, 2 MB by default
maxBigPdfPageSize=2000000

#----------------------------------------------------
# Default dial access number
defaultDialAccessNumber=************

# Default Guest Policy
# Valid values are ALWAYS_ACCEPT, ALWAYS_DENY, ASK_MODERATOR
#
defaultGuestPolicy=ALWAYS_ACCEPT

# Enables or disables authenticated guest
authenticatedGuest=true

#---------------------------------------------------
# Default Meeting Layout
# Valid values are CUSTOM_LAYOUT, SMART_LAYOUT, PRESENTATION_FOCUS, VIDEO_FOCUS
defaultMeetingLayout=SMART_LAYOUT

#
#----------------------------------------------------
# Default welcome message to display when the participant joins the web
# conference. This is only used for the old scheduling which will be
# removed in the future. Use the API to create a conference.
#
# If the message contains characters not in ISO-8859-1 character sets
# they must be properly escaped to unicode characters. An easy way to
# do this is running the native2ascii command setting UTF8 encoding and
# passing this file's path as input and output parameters, e.g.:
#
# native2ascii -encoding UTF8 bigbluebutton.properties bigbluebutton.properties
#
defaultWelcomeMessage=Welcome to <b>%%CONFNAME%%</b>!<br><br>For help on using BigBlueButton see these (short) <a href="https://bigbluebutton.org/teachers/tutorials/"><u>tutorial videos</u></a>.<br><br>To join the audio bridge click the speaker button.  Use a headset to avoid causing background noise for others.
defaultWelcomeMessageFooter= 

# Default maximum number of users a meeting can have.
# Current default is 0 (meeting doesn't have a user limit).
defaultMaxUsers=0

# Maximum number of sessions that each user (extId) can open simultaneously in the same meeting
# when the limit is exceeded, the oldest session will be ended
# if 0, there's no limit
# if 1, limit to only one concurrent access per user
maxUserConcurrentAccesses=3

# Default duration of the meeting in minutes.
# Current default is 0 (meeting doesn't end).
defaultMeetingDuration=0

# Number of minutes to logout client if user
# isn't responsive
clientLogoutTimerInMinutes=0

# End meeting if no user joined within
# a period of time after meeting created.
meetingExpireIfNoUserJoinedInMinutes=30

# Number of minutes to end meeting when
# the last user left.
meetingExpireWhenLastUserLeftInMinutes=15

# User inactivity audit timer interval.
userInactivityInspectTimerInMinutes=0

# Number of minutes to consider a user inactive.
# iSend warning message to client to check if really inactive.
userInactivityThresholdInMinutes=30

# Number of minutes for user to respond to inactivity
# warning before being logged out.
userActivitySignResponseDelayInMinutes=5

# Disable recording by default.
#   true - don't record even if record param in the api call is set to record
#   false - when record param is passed from api, override this default
disableRecordingDefault=false

# Start recording when first user joins the meeting.
# For backward compatibility with 0.81 where whole meeting
# is recorded.
autoStartRecording=true

# Allow the user to start/stop recording.
allowStartStopRecording=true

# Number of minutes that Learning Dashboard will be available after the end of the meeting
# if 0, the Learning Dashboard will keep available permanently
# this is the default value, can be customized using the create API
learningDashboardCleanupDelayInMinutes=2

# Allow webcams streaming reception only to and from moderators
webcamsOnlyForModerator=false

# Per meeting camera share limit
# if 0, there's no limit
meetingCameraCap=0


# Per user camera share limit
# if 0, there's no limit
userCameraCap=3

# Maximum number of cameras pinned simultaneously
maxPinnedCameras=3

# Mute the meeting on start
muteOnStart=false

# Unmute users
# Gives moderators permisson to unmute other users
allowModsToUnmuteUsers=false

# Eject user webcams
# Gives moderators permisson to close other users' webcams
allowModsToEjectCameras=false

# Saves meeting events even if the meeting is not recorded
defaultKeepEvents=false

# Timeout (millis) to remove a joined user after her/his left event without a rejoin
# e.g. regular user left event
# Default 60s
usersTimeout=5000

# Timeout (millis) to remove a joined customer after her/his left event without a rejoin
# e.g. regular customer left event
# Default 60s
customerTimeout=5000

# Timeout (millis) to remove guest users that stopped fetching for her/his status
# e.g. guest that closed the waiting page before being approved
# Default 30s
waitingGuestUsersTimeout=60000

# Timeout (millis) to remove users that called the enter API but did not join
# e.g. user's client hanged between the enter call and join event
# Default 45s
enteredUsersTimeout=45000

#----------------------------------------------------
# This URL is where the BBB client is accessible. When a user sucessfully
# enters a name and password, she is redirected here to load the client.
# Do not commit changes to this field.
bigbluebutton.web.serverURL=https://pbmeetqa.policybazaar.com

#----------------------------------------------------
# Assign URL where the logged-out participant will be redirected after sign-out.
# If "default", it returns to bigbluebutton.web.serverURL
bigbluebutton.web.logoutURL=default

# The url of the BigBlueButton HTML5 client. Users will be redirected here when
# successfully joining the meeting.
defaultHTML5ClientUrl=${bigbluebutton.web.serverURL}/html5client/join

useDefaultLogo=true
defaultLogoURL=${bigbluebutton.web.serverURL}/images/logo.png

# Allow requests without JSESSIONID to be handled (default = false)
allowRequestsWithoutSession=true

# Timeout (seconds) to invalidate inactive HTTP sessions.
# Default: 4 hours.
# For more info, refer to javax.servlet.http.HttpSession#setMaxInactiveInterval 's spec
defaultHttpSessionTimeout=14400

# The url for where the guest will poll if approved to join or not.
defaultGuestWaitURL=${bigbluebutton.web.serverURL}/html5client/guestWait

# The default avatar image to display.
useDefaultAvatar=false
defaultAvatarURL=${bigbluebutton.web.serverURL}/html5client/resources/images/avatar.png

apiVersion=2.0

# Salt which is used by 3rd-party apps to authenticate api calls
securitySalt=330a8b08c3b4c61533e1d0c5ce1ac88f

# List of supported hash algorithms for validating checksums (comma-separated)
# Available options: sha1, sha256, sha384, sha512
supportedChecksumAlgorithms=sha256,sha384,sha512


# Directory where we drop the <meeting-id-recorded>.done file
recordStatusDir=/var/bigbluebutton/recording/status/recorded

redisHost=127.0.0.1
redisPort=6379
redisPassword=
redisKeyExpiry=1209600

# The directory where the published/unpublised recordings are located. This is for
# the get recording* api calls
publishedDir=/var/bigbluebutton/published
unpublishedDir=/var/bigbluebutton/unpublished
captionsDir=/var/bigbluebutton/captions

# when set to true, a single call of getRecordings with no specified meetingID will return a (potentially massive) response listing all recordings on the system
allowFetchAllRecordings=true

# The directory where the pre-built configs are stored
configDir=/var/bigbluebutton/configs

# The directory to export Json with Meeting activities (used in Learning Dashboard)
learningDashboardFilesDir=/var/bigbluebutton/learning-dashboard

# If the API is enabled.
serviceEnabled = true

# Test voiceBridge number
testVoiceBridge=99999
testConferenceMock=conference-mock-default

#------------------------------------------------------
# These properties are used to test the conversion process.
# Conference name folder in ${presentationDir} (see above)
beans.presentationService.testConferenceMock=${testConferenceMock}

# Conference room folder in ${presentationDir}/${testConferenceMock}
beans.presentationService.testRoomMock=conference-mock-default
# Uploaded presentation name
beans.presentationService.testPresentationName=appkonference
# Uploaded presentation file
beans.presentationService.testUploadedPresentation=appkonference.txt
# Default Uploaded presentation file
# to disable default presentation set its value to null
beans.presentationService.defaultUploadedPresentation=${bigbluebutton.web.serverURL}/default.pdf
# Discard default presentation (default.pdf) when Pre-upload Slides are sent within the create call (default true)
beans.presentationService.preUploadedPresentationOverrideDefault=true

presentationBaseURL=${bigbluebutton.web.serverURL}/bigbluebutton/presentation

#----------------------------------------------------
# The URL where the presentations will be loaded from.
#----------------------------------------------------
beans.presentationService.presentationBaseUrl=${presentationBaseURL}
#----------------------------------------------------
# Inject values into grails service beans
beans.presentationService.presentationDir=${presentationDir}

#----------------------------------------------------
# Specify which IPs can do cross domain requests
accessControlAllowOrigin=${bigbluebutton.web.serverURL}

#----------------------------------------------------
# The lapsus of seconds for polling the BBB Server in order to check if it's down.
# After 5 tries if there isn't response, it will be declared down
checkBBBServerEvery=10

# Default settings for breakout rooms
breakoutRoomsRecord=false
breakoutRoomsPrivateChatEnabled=true

# Default Lock Settings
lockSettingsDisableCam=false
lockSettingsDisableMic=false
lockSettingsDisablePrivateChat=false
lockSettingsDisablePublicChat=false
lockSettingsDisableNotes=false
lockSettingsHideUserList=false
lockSettingsLockOnJoin=true
lockSettingsLockOnJoinConfigurable=false
lockSettingsHideViewersCursor=false

defaultTextTrackUrl=${bigbluebutton.web.serverURL}/bigbluebutton

# Param to end the meeting when there are no moderators after a certain period of time.
# Needed for classes where teacher gets disconnected and can't get back in. Prevents
# students from running amok.
endWhenNoModerator=true

# Number of minutes to wait for moderator rejoin before end meeting (if `endWhenNoModerator` enabled)
endWhenNoModeratorDelayInMinutes=1

# List of features to disable (comma-separated)
# Available options:
# chat, sharedNotes, polls, screenshare, externalVideos, presentation, downloadPresentationWithAnnotations
# learningDashboard, layouts, captions, liveTranscription, virtualBackgrounds, customVirtualBackgrounds
# breakoutRooms, importSharedNotesFromBreakoutRooms, importPresentationWithAnnotationsFromBreakoutRooms
disabledFeatures=polls,presentation,learningDashboard,layouts,captions,breakoutRooms,sharedNotes,externalVideos,downloadPresentationWithAnnotations,liveTranscription,customVirtualBackgrounds,importSharedNotesFromBreakoutRooms,importPresentationWithAnnotationsFromBreakoutRooms

# Notify users that recording is on
notifyRecordingIsOn=false

# Allow endpoint with current BigBlueButton version
allowRevealOfBBBVersion=false

# legacy method for disabling of the learning analytics dashboard; please use disabledFeatures=learningDashboard
learningDashboardEnabled=false

# legacy method for disabling of the breakout rooms; please use disabledFeatures=breakoutRooms
breakoutRoomsEnabled=false

# legacy, please use maxUserConcurrentAccesses instead
allowDuplicateExtUserid=true
